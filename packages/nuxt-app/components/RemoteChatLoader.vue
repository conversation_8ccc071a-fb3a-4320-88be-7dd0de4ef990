<template>
  <div class="remote-chat-loader">
    <!-- 统一美观加载界面 -->
    <div
      v-if="showLoadingOverlay"
      class="unified-loading"
      :class="{ visible: showLoadingOverlay }"
    >
      <!-- 背景遮罩 -->
      <div class="loading-backdrop"/>

      <!-- 主要加载内容 -->
      <div class="loading-content">
        <!-- 通用图标容器 -->
        <div class="avatar-container">
          <div class="avatar-ring">
            <div class="avatar-ring-inner"/>
            <div class="avatar-placeholder">
              <div class="placeholder-icon">💬</div>
            </div>
          </div>

          <!-- 脉冲效果 -->
          <div class="pulse-ring pulse-ring-1"/>
          <div class="pulse-ring pulse-ring-2"/>
          <div class="pulse-ring pulse-ring-3"/>
        </div>

        <!-- 加载文本 -->
        <div class="loading-text">
          <h3 class="loading-title">{{ loadingTitle }}</h3>
          <p class="loading-subtitle">{{ loadingSubtitle }}</p>
        </div>

        <!-- 进度指示器 -->
        <div class="progress-container">
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: `${loadingProgress}%` }"
            />
          </div>
          <div class="progress-dots">
            <div
              v-for="i in 3"
              :key="i"
              class="progress-dot"
              :class="{ active: loadingProgress / 33.33 >= i }"
            />
          </div>
        </div>
      </div>

      <!-- 装饰性粒子 -->
      <div class="particles">
        <div
          v-for="i in 12"
          :key="i"
          class="particle"
          :style="{
            '--delay': `${i * 0.2}s`,
            '--angle': `${i * 30}deg`,
          }"
        />
      </div>
    </div>

    <!-- 直接嵌入子应用的iframe -->
    <iframe
      ref="chatIframe"
      :src="chatAppUrl"
      class="chat-iframe"
      :class="{ 'iframe-hidden': showLoadingOverlay }"
      frameborder="0"
      @load="onIframeLoad"
      @error="onIframeError"
    />

    <!-- 错误状态 -->
    <div v-if="error" class="error-overlay">
      <h2>Error Loading Chat</h2>
      <p>{{ error }}</p>
      <p class="error-url">尝试加载: {{ chatAppUrl }}</p>
      <button class="retry-button" @click="retryLoad">Retry</button>
      <button class="redirect-button" @click="redirectToChat"
        >Open in New Tab</button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
  interface Props {
    chatType: 'chat' | 'chat2' | 'chat3' | 'chat4'
    characterId?: string
    storyId?: string
  }

  const props = defineProps<Props>()

  const error = ref<string | null>(null)
  const showLoadingOverlay = ref(true)
  const loadingProgress = ref(0)
  const chatIframe = ref<HTMLIFrameElement | null>(null)
  const loadingTimeout = ref<NodeJS.Timeout | null>(null)

  // 动态加载文案
  const loadingTitle = computed(() => {
    if (loadingProgress.value < 30) return 'Connecting'
    if (loadingProgress.value < 60) return 'Loading Chat'
    if (loadingProgress.value < 90) return 'Preparing Scene'
    return 'Almost Ready'
  })

  const loadingSubtitle = computed(() => {
    if (loadingProgress.value < 30) return 'Connecting to chat service...'
    if (loadingProgress.value < 60) return 'Loading conversation history...'
    if (loadingProgress.value < 90) return 'Preparing chat interface...'
    return 'Get ready to chat!'
  })

  // 构建子应用URL
  const chatAppUrl = computed(() => {
    const runtimeConfig = useRuntimeConfig()
    const baseUrl = runtimeConfig.public.csrAppUrl

    if (!baseUrl) {
      console.error(
        '❌ CSR App URL 未配置，请检查 NUXT_PUBLIC_CSR_APP_URL 环境变量',
      )
      return ''
    }

    let path = ''

    switch (props.chatType) {
      case 'chat':
      case 'chat2':
      case 'chat4':
        path = `/${props.chatType}/${props.storyId}/${props.characterId}`
        break
      case 'chat3':
        path = `/${props.chatType}/${props.characterId}/${props.storyId}`
        break
      default:
        path = `/chat/${props.storyId}/${props.characterId}`
    }

    // 添加重新开始的查询参数
    const route = useRoute()
    const shouldRestart = route.query.restart === '1'
    const queryParams = new URLSearchParams()

    if (shouldRestart) {
      queryParams.set('restart', '1')
    }

    const queryString = queryParams.toString()
    const fullUrl = `${baseUrl}${path}${queryString ? `?${queryString}` : ''}`

    return fullUrl
  })

  // 启动智能加载进度模拟
  const startLoadingProgress = () => {
    loadingProgress.value = 0

    // 模拟初始加载进度
    const progressInterval = setInterval(() => {
      if (loadingProgress.value < 60) {
        loadingProgress.value += Math.random() * 15 + 5 // 5-20的随机增长
      } else if (loadingProgress.value < 80) {
        loadingProgress.value += Math.random() * 8 + 2 // 2-10的随机增长
      } else {
        clearInterval(progressInterval)
      }
    }, 200)

    // 设置最大加载时间，防止无限等待
    loadingTimeout.value = setTimeout(() => {
      if (showLoadingOverlay.value) {
        hideLoadingOverlay()
      }
    }, 8000) // 8秒超时
  }

  // 更新加载进度
  const updateLoadingProgress = (stage: string, progress: number) => {
    const newProgress = Math.max(loadingProgress.value, progress)
    loadingProgress.value = newProgress
  }

  // 隐藏加载遮罩
  const hideLoadingOverlay = () => {
    loadingProgress.value = 100

    setTimeout(() => {
      showLoadingOverlay.value = false
    }, 500) // 延长一点时间让用户看到完成状态

    // 清理定时器
    if (loadingTimeout.value) {
      clearTimeout(loadingTimeout.value)
      loadingTimeout.value = null
    }
  }

  // iframe加载完成
  const onIframeLoad = () => {
    error.value = null

    // iframe加载完成后，开始等待CSR应用的就绪信号
    // 如果3秒内没有收到就绪信号，则强制隐藏加载遮罩
    setTimeout(() => {
      if (showLoadingOverlay.value) {
        hideLoadingOverlay()
      }
    }, 3000)
  }

  // iframe加载错误
  const onIframeError = () => {
    error.value =
      'Failed to load chat application. Please check if the chat service is running.'
    hideLoadingOverlay()
  }

  // 重试加载
  const retryLoad = () => {
    error.value = null

    // 重新加载iframe
    const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
    if (iframe) {
      const currentSrc = iframe.src
      iframe.src = ''
      setTimeout(() => {
        iframe.src = currentSrc
      }, 100)
    }
  }

  // 重定向到新标签页
  const redirectToChat = () => {
    window.open(chatAppUrl.value, '_blank')
  }

  // 处理来自CSR应用的消息
  const handleMessage = (event: MessageEvent) => {
    // 确保消息来自我们的CSR应用
    const runtimeConfig = useRuntimeConfig()
    const csrAppUrl = runtimeConfig.public.csrAppUrl
    if (!csrAppUrl || event.origin !== csrAppUrl) {
      return
    }

    const { type, payload } = event.data

    switch (type) {
      case 'NAVIGATE_TO_HOME':
        // 导航到主应用首页
        navigateTo('/')
        break
      case 'NAVIGATE_TO_STORIES':
        // 导航到故事列表
        navigateTo('/stories')
        break
      case 'NAVIGATE_TO_STORY':
        // 导航到特定故事
        if (payload?.storyId) {
          navigateTo(`/story/${payload.storyId}`)
        }
        break
      case 'NAVIGATE_TO_NUXT_PAGE':
        // 导航到Nuxt中的其他页面
        if (payload?.path) {
          navigateTo(payload.path)
        }
        break
      case 'GO_BACK':
        // 返回上一页
        if (import.meta.client) {
          window.history.back()
        }
        break
      case 'SYNC_AUTH_STATE':
        // 同步认证状态
        handleAuthStateSync(payload)
        break
      case 'SYNC_USER_STATE':
        // 同步用户状态
        handleUserStateSync(payload)
        break
      case 'REQUEST_STATE':
        // CSR应用请求状态
        sendStateToCSR()
        break
      case 'REQUEST_STORY_DATA':
        // CSR应用请求故事数据
        handleStoryDataRequest(payload)
        break
      case 'SYNC_STORY_STATE':
        // 同步故事状态
        handleStoryStateSync(payload)
        break
      case 'SOCIAL_LOGIN_REQUEST':
        // 处理来自CSR应用的社交登录请求
        handleSocialLoginRequest(payload)
        break
      case 'LOADING_PROGRESS':
        // 接收CSR应用的加载进度
        updateLoadingProgress(payload.stage || 'unknown', payload.progress || 0)
        break
      case 'CSR_APP_READY':
        // CSR应用已就绪
        updateLoadingProgress('app-ready', 80)
        break
      case 'CSR_ROUTE_READY':
        // CSR应用路由已就绪
        updateLoadingProgress('route-ready', 100)
        setTimeout(() => hideLoadingOverlay(), 300)
        break
      default:
        console.warn('未知的消息类型:', type)
    }
  }

  // 状态同步处理函数
  const handleAuthStateSync = (authData: any) => {
    console.log('🔐 主应用: 接收到认证状态同步', authData)

    // 这里可以更新主应用的认证状态
    // 例如：更新token到localStorage或store
    if (authData.token) {
      localStorage.setItem('token', authData.token)
    }
    if (authData.refreshToken) {
      localStorage.setItem('refreshToken', authData.refreshToken)
    }
    if (authData.userId) {
      localStorage.setItem('userId', authData.userId)
    }
  }

  const handleUserStateSync = (userData: any) => {
    console.log('👤 主应用: 接收到用户状态同步', userData)

    // 这里可以更新主应用的用户状态
    // 例如：更新用户信息到store
    if (userData.user) {
      localStorage.setItem('userInfo', JSON.stringify(userData.user))
    }
    if (userData.language) {
      localStorage.setItem('language', userData.language)
    }
    if (userData.theme) {
      localStorage.setItem('theme', userData.theme)
    }
  }

  // 处理来自CSR应用的社交登录请求
  const handleSocialLoginRequest = async (payload: any) => {
    console.log('🔐 主应用: 接收到社交登录请求', payload)

    try {
      const { type, social_redirect_url, app_redirect_url } = payload

      // 存储重定向URL
      if (import.meta.client && app_redirect_url) {
        sessionStorage.setItem('login_redirect', app_redirect_url)
      }

      // 调用主应用的社交登录API
      const config = useRuntimeConfig()
      const response = await $fetch(
        `${config.public.apiBase}/api/v1/social-login.get-url`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: {
            login_type: type,
            redirect_url: social_redirect_url,
          },
        },
      )

      if (response.code !== '0' || !response.data?.url) {
        throw new Error('Failed to get social login URL')
      }

      const loginUrl = response.data.url
      if (!loginUrl.startsWith('http')) {
        throw new Error('Invalid login URL')
      }

      // 在主应用中跳转到社交登录页面
      window.location.href = loginUrl
    } catch (error: any) {
      console.error('社交登录请求处理失败:', error)
      // 可以通过postMessage通知CSR应用登录失败
      const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
      if (iframe && iframe.contentWindow) {
        const runtimeConfig = useRuntimeConfig()
        iframe.contentWindow.postMessage(
          {
            type: 'SOCIAL_LOGIN_ERROR',
            error: error.message || 'Social login failed',
          },
          runtimeConfig.public.csrAppUrl,
        )
      }
    }
  }

  // 处理故事数据请求
  const handleStoryDataRequest = async (payload: any) => {
    console.log('📚 主应用: 收到故事数据请求', payload)

    const { storyId, actorId } = payload

    try {
      // 使用 Nuxt 的 $fetch 来获取故事数据
      const config = useRuntimeConfig()
      const [storyResponse, actorResponse] = await Promise.all([
        $fetch(`${config.public.apiBase}/api/v1/stories/${storyId}`),
        $fetch(`${config.public.apiBase}/api/v1/stories/${storyId}/actors`),
      ])

      let story = null
      let actor = null

      if (storyResponse.code === '0') {
        story = storyResponse.data.story
      }

      if (actorResponse.code === '0') {
        actor = actorResponse.data.actors?.find((a: any) => a.id === actorId)
      }

      // 发送数据到CSR应用
      const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
      if (iframe && iframe.contentWindow) {
        iframe.contentWindow.postMessage(
          {
            type: 'STORY_DATA_RESPONSE',
            payload: { story, actor },
            timestamp: Date.now(),
            source: 'nuxt-app',
          },
          config.public.csrAppUrl,
        )
        console.log('📚 主应用: 已发送故事数据到CSR应用', { story, actor })
      }
    } catch (error) {
      console.error('获取故事数据失败:', error)
      // 发送错误响应
      const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
      if (iframe && iframe.contentWindow) {
        const config = useRuntimeConfig()
        iframe.contentWindow.postMessage(
          {
            type: 'STORY_DATA_ERROR',
            error: error.message || 'Failed to fetch story data',
            timestamp: Date.now(),
            source: 'nuxt-app',
          },
          config.public.csrAppUrl,
        )
      }
    }
  }

  // 处理故事状态同步
  const handleStoryStateSync = (payload: any) => {
    console.log('📚 主应用: 收到故事状态同步', payload)

    // 这里可以将故事状态保存到主应用的状态管理中
    // 例如：存储到 localStorage 或 store
    if (payload.currentStory) {
      localStorage.setItem('currentStory', JSON.stringify(payload.currentStory))
    }
    if (payload.currentActor) {
      localStorage.setItem('currentActor', JSON.stringify(payload.currentActor))
    }
  }

  const sendStateToCSR = () => {
    console.log('📤 主应用: 发送状态到CSR应用')

    // 获取配置
    const config = useRuntimeConfig()

    // 获取主应用的状态
    const authState = {
      token: localStorage.getItem('token'),
      refreshToken: localStorage.getItem('refreshToken'),
      userId: localStorage.getItem('userId'),
    }

    const userState = {
      user: localStorage.getItem('userInfo')
        ? JSON.parse(localStorage.getItem('userInfo')!)
        : null,
      language: localStorage.getItem('language'),
      theme: localStorage.getItem('theme'),
    }

    const storyState = {
      currentStory: localStorage.getItem('currentStory')
        ? JSON.parse(localStorage.getItem('currentStory')!)
        : null,
      currentActor: localStorage.getItem('currentActor')
        ? JSON.parse(localStorage.getItem('currentActor')!)
        : null,
    }

    // 发送状态到CSR应用
    const iframe = document.querySelector('.chat-iframe') as HTMLIFrameElement
    if (iframe && iframe.contentWindow) {
      iframe.contentWindow.postMessage(
        {
          type: 'STATE_SYNC',
          payload: {
            auth: authState,
            user: userState,
            story: storyState,
          },
          timestamp: Date.now(),
          source: 'nuxt-app',
        },
        config.public.csrAppUrl,
      )
    }
  }

  // 组件挂载时的事件监听
  onMounted(() => {
    // 启动加载进度模拟
    startLoadingProgress()

    // 监听来自CSR应用的消息
    if (import.meta.client) {
      window.addEventListener('message', handleMessage)
    }

    // 延迟发送初始状态到CSR应用
    setTimeout(() => {
      sendStateToCSR()
    }, 1000)
  })

  // 组件卸载时清理事件监听
  onUnmounted(() => {
    if (import.meta.client) {
      window.removeEventListener('message', handleMessage)
    }

    // 清理定时器
    if (loadingTimeout.value) {
      clearTimeout(loadingTimeout.value)
      loadingTimeout.value = null
    }
  })

  // 监听props变化，更新URL
  watch(
    [() => props.chatType, () => props.storyId, () => props.characterId],
    () => {
      error.value = null
    },
  )
</script>

<style scoped>
  .remote-chat-loader {
    width: 100%;
    height: 100vh;
    position: relative;
    overflow: hidden;
  }

  .chat-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: #000;
    transition: opacity 0.3s ease;
  }

  .iframe-hidden {
    opacity: 0;
    pointer-events: none;
  }

  /* 统一美观加载界面样式 */
  .unified-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &.visible {
      opacity: 1;
      visibility: visible;
    }
  }

  .loading-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(31, 0, 56, 0.95) 0%,
      rgba(76, 60, 89, 0.95) 50%,
      rgba(124, 77, 255, 0.95) 100%
    );
    backdrop-filter: blur(20px);
  }

  .loading-content {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 2;
    padding: 40px 20px;
  }

  /* 头像容器 */
  .avatar-container {
    position: relative;
    margin-bottom: 32px;
  }

  .avatar-ring {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(45deg, #7c4dff, #ff4081, #00bcd4);
    padding: 4px;
    animation: rotate 3s linear infinite;

    .avatar-ring-inner {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: #1f0038;
      padding: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }

    .avatar-placeholder {
      width: calc(100% - 16px);
      height: calc(100% - 16px);
      border-radius: 50%;
      background: linear-gradient(135deg, #7c4dff, #ff4081);
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      /* 反向旋转，抵消父元素的旋转 */
      animation: counter-rotate 3s linear infinite;

      .placeholder-icon {
        font-size: 48px;
        opacity: 0.8;
      }
    }
  }

  .pulse-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(124, 77, 255, 0.3);
    border-radius: 50%;
    animation: pulse 2s ease-out infinite;

    &.pulse-ring-1 {
      width: 140px;
      height: 140px;
      animation-delay: 0s;
    }

    &.pulse-ring-2 {
      width: 160px;
      height: 160px;
      animation-delay: 0.7s;
    }

    &.pulse-ring-3 {
      width: 180px;
      height: 180px;
      animation-delay: 1.4s;
    }
  }

  /* 加载文本 */
  .loading-text {
    text-align: center;
    margin-bottom: 32px;

    .loading-title {
      font-family: 'Work Sans', sans-serif;
      font-size: 24px;
      font-weight: 600;
      color: #ffffff;
      margin: 0 0 8px 0;
      letter-spacing: 0.5px;
    }

    .loading-subtitle {
      font-family: 'Work Sans', sans-serif;
      font-size: 14px;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.7);
      margin: 0;
      line-height: 1.4;
    }
  }

  /* 进度指示器 */
  .progress-container {
    width: 240px;

    .progress-bar {
      width: 100%;
      height: 4px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 16px;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #7c4dff, #ff4081);
        border-radius: 2px;
        transition: width 0.3s ease;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 20px;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.3)
          );
          animation: shimmer 1.5s ease-in-out infinite;
        }
      }
    }

    .progress-dots {
      display: flex;
      justify-content: center;
      gap: 12px;

      .progress-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;

        &.active {
          background: #7c4dff;
          box-shadow: 0 0 12px rgba(124, 77, 255, 0.6);
          transform: scale(1.2);
        }
      }
    }
  }

  /* 装饰性粒子 */
  .particles {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 400px;
    pointer-events: none;

    .particle {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 4px;
      height: 4px;
      background: rgba(124, 77, 255, 0.6);
      border-radius: 50%;
      transform-origin: 0 150px;
      animation: orbit 8s linear infinite;
      animation-delay: var(--delay);
      transform: translate(-50%, -50%) rotate(var(--angle));

      &::before {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        width: 6px;
        height: 6px;
        background: rgba(255, 64, 129, 0.4);
        border-radius: 50%;
        animation: glow 2s ease-in-out infinite alternate;
      }
    }
  }

  .error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    z-index: 10;
  }

  .error-overlay h2 {
    color: #e74c3c;
    margin-bottom: 1rem;
  }

  .error-overlay p {
    color: #ccc;
    text-align: center;
    margin-bottom: 1.5rem;
    max-width: 400px;
  }

  .retry-button,
  .redirect-button {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    margin: 0.5rem;
    transition: background-color 0.2s;
  }

  .retry-button:hover,
  .redirect-button:hover {
    background: #2980b9;
  }

  .redirect-button {
    background: #27ae60;
  }

  .redirect-button:hover {
    background: #229954;
  }

  /* CSS动画 */
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes counter-rotate {
    from {
      transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
      transform: translate(-50%, -50%) rotate(-360deg);
    }
  }

  @keyframes pulse {
    0% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.7;
    }
    100% {
      transform: translate(-50%, -50%) scale(1.2);
      opacity: 0;
    }
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-20px);
    }
    100% {
      transform: translateX(240px);
    }
  }

  @keyframes orbit {
    from {
      transform: translate(-50%, -50%) rotate(var(--angle)) translateY(-150px)
        rotate(calc(-1 * var(--angle)));
    }
    to {
      transform: translate(-50%, -50%) rotate(calc(var(--angle) + 360deg))
        translateY(-150px) rotate(calc(-1 * (var(--angle) + 360deg)));
    }
  }

  @keyframes glow {
    from {
      opacity: 0.4;
      transform: scale(1);
    }
    to {
      opacity: 0.8;
      transform: scale(1.5);
    }
  }

  /* 响应式设计 */
  @media (max-width: 480px) {
    .loading-content {
      padding: 20px;
    }

    .avatar-ring {
      width: 100px;
      height: 100px;
    }

    .pulse-ring {
      &.pulse-ring-1 {
        width: 120px;
        height: 120px;
      }
      &.pulse-ring-2 {
        width: 140px;
        height: 140px;
      }
      &.pulse-ring-3 {
        width: 160px;
        height: 160px;
      }
    }

    .loading-title {
      font-size: 20px;
    }

    .progress-container {
      width: 200px;
    }

    .particles {
      width: 250px;
      height: 250px;
    }
  }
</style>
