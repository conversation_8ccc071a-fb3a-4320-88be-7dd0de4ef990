<template>
  <div class="story-intro-container">
    <!-- 主要内容 -->
    <div class="story-intro" :class="{ loading: imageLoading }">
      <!-- 背景图片 -->
      <div v-if="storyDetail?.preview_url" class="background-image">
        <NuxtImg
          :src="storyDetail.preview_url"
          alt="Story Preview"
          loading="eager"
          @load="imageLoading = false"
          @error="handleImageError"
        />
      </div>

      <!-- 骨架屏 -->
      <div v-if="imageLoading" class="skeleton-loading" />

      <!-- 返回按钮 -->
      <div class="back-button" @click="$router.push('/')">
        <Icon name="lucide:arrow-left" size="24" />
      </div>

      <!-- 内容区域 -->
      <div class="content">
        <h1 class="title" :data-text="storyDetail?.title">
          {{ storyDetail?.title }}
        </h1>

        <div class="description">
          <div>Description</div>
          {{ storyDetail?.description }}
        </div>
      </div>

      <!-- 底部操作区域 -->
      <div class="bottom-actions">
        <!-- 收藏按钮 -->
        <div class="favorite-button" :class="{ active: isFavorited }" @click="toggleFavorite">
          <div ref="favoriteIconRef" class="heart-icon">
            <span v-if="isDetailLoading" class="heart-loading-spinner" />
            <template v-else>
              <Icon v-if="isFavorited" name="lucide:heart" size="24" />
              <Icon v-else name="lucide:heart" size="24" />
            </template>
          </div>
          <span>{{ isFavorited ? 'Favorited!' : 'Add Favorite' }}</span>
        </div>

        <!-- 播放按钮 -->
        <button class="play-button" :disabled="isPlayLoading" @click="handlePlay">
          <span v-if="isPlayLoading || isDetailLoading" class="loading-spinner" />
          <template v-else>
            Play
            <span v-if="showDiamonds" class="cost" :class="{ discount: isDiscount }">
              <NuxtImg
                :src="icons.diamond.value"
                class="diamond-icon"
                alt="Diamond icon"
                loading="lazy"
              />
              <template v-if="isDiscount">
                <span class="original-price">{{ Math.floor(storyCost * 5) }}</span>
                <span class="discount-price">{{ storyCost }}</span>
              </template>
              <template v-else>
                {{ storyCost }}
              </template>
            </span>
            <span v-else-if="isStorePurchased" class="status unlocked"> Unlocked </span>
            <span v-else-if="isFreeStory" class="status free"> Free </span>
          </template>
        </button>
      </div>

      <!-- 角色选择 - 移动端使用Drawer，PC端使用Modal -->
      <CharacterSelectDrawer
        v-if="showCharacterSelect && shouldUseMobileLayout"
        :visible="showCharacterSelect"
        :actors="storyDetail?.actors || []"
        :story-coins="storyDetail?.coins || 0"
        :story-is-purchased="storyDetail?.is_purchased || false"
        @select="handleSelectCharacter"
        @close="showCharacterSelect = false"
      />

      <CharacterSelectModal
        v-if="showCharacterSelect && shouldUsePCLayout"
        :visible="showCharacterSelect"
        :actors="storyDetail?.actors || []"
        :story-coins="storyDetail?.coins || 0"
        :story-is-purchased="storyDetail?.is_purchased || false"
        @select="handleSelectCharacter"
        @close="showCharacterSelect = false"
      />

      <!-- 聊天历史确认对话框 -->
      <ChatHistoryConfirmDialog
        :visible="showDialog"
        @continue="handleContinue"
        @restart="handleRestart"
        @close="handleDialogClose"
      />
    </div>
  </div>
</template>

<script setup>
// 定义页面元数据
definePageMeta({
  layout: 'mobile'
})

// 获取路由参数
const route = useRoute()
const router = useRouter()
const storyId = route.params.id

// Composables
const { getStoryDetail, toggleFavorite: toggleStoryFavorite } = useStories()
const { shouldUseMobileLayout, shouldUsePCLayout } = useDeviceDetection()
const { icons } = useCdn()

// 状态管理
const isDetailLoading = ref(false)
const isPlayLoading = ref(false)
const imageLoading = ref(true)
const showCharacterSelect = ref(false)
const showDialog = ref(false)

// 计算属性
const storyCost = computed(() => storyDetail.value?.coins || 0)
const isStorePurchased = computed(() => storyDetail.value?.is_purchased || false)
const isFreeStory = computed(() => storyCost.value === 0)
const showDiamonds = computed(() => !isStorePurchased.value && storyCost.value > 0)
const isDiscount = computed(() => false) // 可以根据需要实现折扣逻辑

// 方法
const handleImageError = () => {
  imageLoading.value = false
  console.error('Failed to load story preview image')
}

const toggleFavorite = async () => {
  try {
    isDetailLoading.value = true

    // 调用真实的API
    await toggleStoryFavorite(String(storyId), isFavorited.value)

    // 更新本地状态
    isFavorited.value = !isFavorited.value

    // 同时更新storyDetail中的收藏状态
    if (storyDetail.value) {
      storyDetail.value.is_fav = isFavorited.value
    }

    console.log('Toggle favorite success:', isFavorited.value)
  } catch (error) {
    console.error('Failed to toggle favorite:', error)
    // 可以显示错误提示
  } finally {
    isDetailLoading.value = false
  }
}

const selectedActor = ref(null)

const handlePlay = async () => {
  try {
    isPlayLoading.value = true

    const actors = storyDetail.value?.actors || []

    // 如果没有角色且不是版本3的游戏，显示错误
    if (actors.length === 0 && storyDetail.value?.version !== '3') {
      console.error('No characters available')
      return
    }

    // 如果是版本3的游戏，直接选择第一个角色并开始游戏
    if (storyDetail.value?.version === '3') {
      const actor = {
        id: 'reasoning',
        name: 'Reasoning'
      }
      await processSelectedActor(actor)
      return
    }

    // 如果只有一个角色，自动选择
    if (actors.length === 1) {
      await processSelectedActor(actors[0])
      return
    }

    // 如果有多个角色，显示角色选择
    showCharacterSelect.value = true
  } catch (error) {
    console.error('Failed to start game:', error)
  } finally {
    isPlayLoading.value = false
  }
}

const handleSelectCharacter = async (actor) => {
  showCharacterSelect.value = false
  isPlayLoading.value = true
  try {
    await processSelectedActor(actor)
  } finally {
    isPlayLoading.value = false
  }
}

// 处理选择的角色的通用逻辑
const processSelectedActor = async (actor) => {
  selectedActor.value = actor

  // 检查聊天历史
  await getChatHistory()

  // 如果不需要重新开始，直接返回（等待用户选择继续或重新开始）
  if (!shouldRestart.value) return

  // 直接开始新游戏
  await navigateToChat(selectedActor.value, true)
}

// 检查聊天历史
const shouldRestart = ref(true)
const getChatHistory = async () => {
  try {
    // 只有当角色版本为2、3或4，或故事版本为3时才检查聊天历史记录
    if (
      selectedActor.value?.version === '2' ||
      selectedActor.value?.version === '3' ||
      selectedActor.value?.version === '4' ||
      storyDetail.value?.version === '3'
    ) {
      // 调用真实的API检查聊天历史
      const { getUserChatHistory } = useApi()
      const response = await getUserChatHistory(
        storyDetail.value?.id,
        selectedActor.value?.id,
        storyDetail.value?.version
      )

      if (response.code === '0' && response.data?.history?.length > 0) {
        shouldRestart.value = false
        showDialog.value = true
        showCharacterSelect.value = false
        return // 重要：这里要return，不要继续执行后面的逻辑
      } else {
        shouldRestart.value = true
      }
    } else {
      // 如果角色版本不是2、3或4，直接设置为重新开始
      shouldRestart.value = true
    }
  } catch (error) {
    console.error('Failed to check chat history:', error)
    shouldRestart.value = true
    // 不要throw error，让游戏继续
  }
}

const handleContinue = async () => {
  showDialog.value = false

  if (!selectedActor.value) return

  shouldRestart.value = false
  await navigateToChat(selectedActor.value, false)
}

const handleRestart = async () => {
  showDialog.value = false

  if (!selectedActor.value) return

  shouldRestart.value = true
  await navigateToChat(selectedActor.value, true)
}

// 处理对话框关闭（不选择继续或重新开始）
const handleDialogClose = () => {
  showDialog.value = false
  isPlayLoading.value = false
  // 重置选中的角色
  selectedActor.value = null
  // 如果是多个角色的情况，重新显示角色选择
  if (storyDetail.value?.actors?.length > 1) {
    showCharacterSelect.value = true
  }
}

const navigateToChat = async (actor, isRestart) => {
  try {
    // 根据故事和角色配置决定使用哪个chat版本
    const chatVersion = determineChatVersion(storyDetail.value, actor)

    // 构建主应用的chat路由（会通过iframe加载CSR应用）
    const chatRoute = buildChatRoute(chatVersion, storyId, actor?.id || 'default')

    console.log('📱 移动端导航到主应用chat页面:', {
      version: chatVersion,
      route: chatRoute,
      restart: isRestart
    })

    // 导航到主应用的chat页面（使用iframe）
    await router.push({
      path: chatRoute,
      query: {
        restart: isRestart ? '1' : '0'
      }
    })
  } catch (error) {
    console.error('Failed to navigate to chat:', error)
  }
}

// 根据故事和角色配置决定chat版本
const determineChatVersion = (story, actor) => {
  if (!story) return 'chat'

  // 优先根据故事版本判断
  if (story.version === '3') {
    return 'chat3' // 故事版本3使用chat3 (Telepathy)
  }

  // 然后根据角色版本判断
  if (actor) {
    if (actor.version === '4') {
      return 'chat4' // 角色版本4使用chat4 (Live)
    } else if (actor.version === '2') {
      return 'chat2' // 角色版本2使用chat2 (Enhanced)
    }
  }

  // 兼容旧的ID判断逻辑（作为备选方案）
  if (story.id === '1') return 'chat4'
  if (story.id === '2') return 'chat3'
  if (story.id === '3') return 'chat2'

  return 'chat' // 默认使用chat (Classic)
}

// 构建chat路由
const buildChatRoute = (version, storyId, actorId) => {
  switch (version) {
    case 'chat2':
      return `/chat2/${storyId}/${actorId}`
    case 'chat3':
      return `/chat3/${storyId}/${actorId}` // chat3使用标准参数顺序
    case 'chat4':
      return `/chat4/${storyId}/${actorId}`
    default:
      return `/chat/${storyId}/${actorId}`
  }
}

// 在服务端和客户端都预加载故事数据（用于 SEO）
const { data: storyDetail, pending: isLoading } = await useLazyAsyncData(
  `story-${storyId}`,
  async () => {
    try {
      const story = await getStoryDetail(String(storyId))
      return story
    } catch {
      return null
    }
  },
  {
    server: true, // 在服务端执行
    default: () => null
  }
)

// 响应式的收藏状态
const isFavorited = ref(false)

// 监听故事数据变化，更新收藏状态
watch(storyDetail, (newStory) => {
  if (newStory) {
    isFavorited.value = newStory.is_fav || false
  } else if (newStory === null && !isLoading.value) {
    // 如果数据加载完成但为null，说明加载失败，跳转到首页
    console.error('Failed to load story details')
    router.push('/')
  }
}, { immediate: true })

// 生命周期 - 现在不需要手动加载数据了
onMounted(() => {
  // 数据已经通过 useLazyAsyncData 加载，这里不需要重复加载
  console.log('Story detail page mounted, data already loaded via useLazyAsyncData')
})

// 获取品牌配置用于SEO
const { brandingConfig } = useBranding()

// SEO配置 - 动态获取故事标题和描述
useSeoMeta({
  title: computed(() => {
    if (storyDetail.value?.title) {
      return `${storyDetail.value.title} - ${brandingConfig.value.websiteTitle}`
    }
    return `Story - ${brandingConfig.value.websiteTitle}`
  }),
  description: computed(() => storyDetail.value?.description || 'Interactive AI Story'),
  keywords: computed(() => {
    const tags = storyDetail.value?.tags || []
    const baseKeywords = 'AI story, interactive fiction, virtual dating, otome game'
    return tags.length > 0 ? `${baseKeywords}, ${tags.join(', ')}` : baseKeywords
  }),
  ogTitle: computed(() => {
    if (storyDetail.value?.title) {
      return `${storyDetail.value.title} - ${brandingConfig.value.websiteTitle}`
    }
    return `Story - ${brandingConfig.value.websiteTitle}`
  }),
  ogDescription: computed(() => storyDetail.value?.description || 'Interactive AI Story'),
  ogImage: computed(() => storyDetail.value?.preview_url || brandingConfig.value.ogImageUrl),
  ogType: 'website',
  ogUrl: computed(() => `${brandingConfig.value.siteUrl}/story/${storyId}`),
  twitterCard: 'summary_large_image',
  twitterTitle: computed(() => {
    if (storyDetail.value?.title) {
      return `${storyDetail.value.title} - ${brandingConfig.value.websiteTitle}`
    }
    return `Story - ${brandingConfig.value.websiteTitle}`
  }),
  twitterDescription: computed(() => storyDetail.value?.description || 'Interactive AI Story'),
  twitterImage: computed(() => storyDetail.value?.preview_url || brandingConfig.value.ogImageUrl)
})
</script>

<style scoped>
.story-intro-container {
  position: relative;
  height: calc(var(--vh, 1vh) * 100);
}

.story-intro {
  height: calc(var(--vh, 1vh) * 100);
  background: #1f0038;
  color: white;
  padding: 20px 16px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  opacity: 1;
  transition: opacity 0.6s ease;
}

.story-intro.loading {
  .background-image img {
    opacity: 0;
  }
}

.background-image {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.background-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.6s ease;
}

.background-image::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    180deg,
    rgba(31, 0, 56, 0) 0%,
    rgba(31, 0, 56, 0.5) 50%,
    rgba(31, 0, 56, 0.8) 100%
  );
  z-index: 1;
}

.story-intro:not(.loading) .background-image img {
  opacity: 1;
}

.skeleton-loading {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #1f0038;
  overflow: hidden;
  z-index: 0;
}

.skeleton-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -150%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.08), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(250%);
  }
}

.back-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  border-radius: 50%;
  margin-bottom: 24px;
  cursor: pointer;
  position: relative;
  z-index: 1;
}

.back-button svg {
  stroke: #000;
}

.back-button:active {
  background: rgba(255, 255, 255, 0.2);
}

.content {
  margin-top: auto;
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
}

.title {
  font-size: 32px;
  font-weight: 800;
  margin: 0 0 16px;
  line-height: 1.2;
  position: relative;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.title::before {
  content: attr(data-text);
  position: absolute;
  -webkit-text-stroke: 4px #1f0038;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  color: transparent;
  filter: blur(0.3px);
}

.title::after {
  content: attr(data-text);
  position: absolute;
  -webkit-text-stroke: 6px #1f0038;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
  color: transparent;
  filter: blur(1px);
}

.description {
  font-size: 16px;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.8);
}

.description > div:first-child {
  font-weight: 600;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.9);
}

.bottom-actions {
  position: relative;
  z-index: 1;
}

.favorite-button {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 16px;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.heart-icon {
  transform-origin: center;
  will-change: transform;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.heart-loading-spinner {
  width: 56px;
  height: 56px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 0.8s linear infinite;
}

.favorite-button:active .heart-icon {
  transform: scale(0.95);
}

.favorite-button span {
  font-size: 14px;
  font-weight: 500;
  color: white;
}

.play-button {
  width: 100%;
  border-radius: 40px;
  background: #ca93f2;
  border: none;
  color: #241d49;
  font-size: 15px;
  font-weight: 600;
  padding: 14px;
  cursor: pointer;
  transition: transform 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.play-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.play-button:active:not(:disabled) {
  transform: scale(0.98);
}

.play-button .loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(36, 29, 73, 0.3);
  border-radius: 50%;
  border-top-color: #241d49;
  animation: spin 0.8s linear infinite;
}

.cost {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(36, 29, 73, 0.2);
  padding: 4px 10px;
  border-radius: 16px;
  margin-left: 4px;
  position: relative;
}

.cost .diamond-icon {
  width: 16px;
  height: 16px;
}

.cost.discount {
  background: rgba(36, 29, 73, 0.2);
  border: 1px solid rgba(202, 147, 242, 0.3);
  padding: 4px 10px;
  position: relative;
  overflow: visible;
  margin-left: 4px;
}

.cost.discount::after {
  content: '-80%';
  position: absolute;
  top: -10px;
  right: -10px;
  background: #ff3b30;
  color: white;
  font-size: 9px;
  font-weight: bold;
  padding: 2px 5px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transform: rotate(0);
  z-index: 1;
  animation: pulse 1.5s infinite;
}

.cost.discount .original-price {
  text-decoration: line-through;
  opacity: 0.6;
  font-size: 13px;
  position: relative;
  margin-right: 5px;
}

.cost.discount .discount-price {
  color: #ffeb3b;
  font-weight: bold;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
  font-size: 13px;
  position: relative;
  z-index: 2;
}

.status {
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 13px;
  margin-left: 4px;
}

.status.unlocked {
  background: rgba(36, 29, 73, 0.2);
  color: #ffffff;
}

.status.free {
  background: rgba(36, 29, 73, 0.2);
  color: #daff96;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
</style>
