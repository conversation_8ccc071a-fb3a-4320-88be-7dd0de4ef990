<template>
  <div class="login-container">
    <div class="back-button" @click="router.push('/')">
      <Icon name="lucide:x" size="18" />
    </div>

    <div class="login-content">
      <div class="logo-wrapper">
        <NuxtImg :src="logoUrl" :alt="websiteTitle" loading="eager" />
        <h2>Sign up to continue !</h2>
      </div>

      <div class="form-wrapper">
        <div class="social-buttons">
          <div class="social-row">
            <div class="social-item">
              <button
                class="social-button google"
                :disabled="loading"
                @click="handleSocialLogin('google')"
              >
                <Icon name="lucide:chrome" size="24" />
              </button>
              <span class="social-name">Google</span>
            </div>
            <div class="social-item">
              <button
                class="social-button discord"
                :disabled="loading"
                @click="handleSocialLogin('discord')"
              >
                <Icon name="lucide:message-circle" size="24" />
              </button>
              <span class="social-name">Discord</span>
            </div>
            <div class="social-item">
              <button
                class="social-button facebook"
                :disabled="loading"
                @click="handleSocialLogin('facebook')"
              >
                <Icon name="lucide:facebook" size="24" />
              </button>
              <span class="social-name">Facebook</span>
            </div>
          </div>
        </div>

        <div class="divider">
          <span>or</span>
        </div>

        <div class="input-group">
          <input
            v-model="form.email"
            type="email"
            placeholder="Enter your email"
            :class="{ error: errors?.email }"
            @blur="validateEmail"
          />
          <span v-if="errors?.email" class="error-text">{{
            errors.email
          }}</span>
        </div>

        <div class="input-group verification-group">
          <input
            v-model="form.verificationCode"
            type="text"
            placeholder="Enter code"
            :class="{ error: errors?.verificationCode }"
          />
          <button
            class="send-code-button"
            :class="{ 'is-inactive': !form.email }"
            :disabled="loading || countdown > 0"
            @click="handleSendCode"
          >
            {{ countdown > 0 ? `Resend (${countdown}s)` : 'Send' }}
          </button>
          <span v-if="errors?.verificationCode" class="error-text">{{
            errors.verificationCode
          }}</span>
        </div>

        <button
          class="continue-button"
          :class="{ 'is-inactive': !form.email || !form.verificationCode }"
          :disabled="loading"
          @click="handleLogin"
        >
          <span v-if="!loading">Continue</span>
          <Icon v-else name="lucide:loader-2" class="animate-spin" size="20" />
        </button>
      </div>
    </div>

    <div class="terms-text">
      By signing in you agree with our<br />
      <NuxtLink to="/terms">Terms of Service</NuxtLink>,
      <NuxtLink to="/privacy">Privacy Policy</NuxtLink>,
      <NuxtLink to="/complaints">Complaints Policy</NuxtLink>,
      <NuxtLink to="/content-removal">Content Removal Policy</NuxtLink>,
      <NuxtLink to="/record-keeping">18 U.S.C. 2257 Compliance</NuxtLink>,
      <NuxtLink to="/about">About Us</NuxtLink>,
      <NuxtLink to="/refund">Refund and Returns Policy</NuxtLink>.
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  layout: false,
})

// 运行时配置
const config = useRuntimeConfig()
const logoUrl =
  config.public?.logoUrl ||
  'https://static.reelplay.ai/static/images/logo/reelplay_logo.png'
const websiteTitle = config.public?.websiteTitle || 'PlayShot'

// 路由和状态管理
const router = useRouter()
const userStore = useUserStore()
const { success: showSuccess, error: showError } = useMessage()

// 同步用户状态到其他应用
const syncUserStateToApps = async () => {
  if (!import.meta.client) return

  try {
    console.log('[AUTH_SYNC] syncUserStateToApps: 开始同步状态')

    // 同步认证状态
    const authData = {
      token: userStore.token,
      refreshToken: userStore.refreshToken,
      userId: userStore.userId,
      isAuthenticated: userStore.isAuthenticated,
      isGuest: userStore.isGuest,
    }

    // 同步用户信息
    const userData = {
      user: userStore.userInfo,
      language: 'en', // 可以从用户设置中获取
      theme: 'dark', // 可以从用户设置中获取
    }

    console.log('[AUTH_SYNC] syncUserStateToApps: 准备同步的数据', {
      hasToken: !!authData.token,
      hasUserId: !!authData.userId,
      isAuthenticated: authData.isAuthenticated,
      hasUserInfo: !!userData.user,
    })

    // 如果在iframe环境中，通知父应用
    if (window.parent !== window) {
      console.log('[AUTH_SYNC] syncUserStateToApps: 在iframe环境中，通知父应用')
      window.parent.postMessage(
        {
          type: 'AUTH_STATE_SYNC',
          data: authData,
        },
        '*',
      )

      window.parent.postMessage(
        {
          type: 'USER_STATE_SYNC',
          data: userData,
        },
        '*',
      )
    } else {
      // 如果在主应用中，查找并通知所有iframe中的CSR应用
      console.log('[AUTH_SYNC] syncUserStateToApps: 在主应用中，查找iframe')
      const chatIframes = document.querySelectorAll('.chat-iframe')
      const config = useRuntimeConfig()

      console.log(
        '[AUTH_SYNC] syncUserStateToApps: 找到iframe数量',
        chatIframes.length,
      )

      chatIframes.forEach((iframe, index) => {
        if (iframe instanceof HTMLIFrameElement && iframe.contentWindow) {
          console.log(
            `[AUTH_SYNC] syncUserStateToApps: 向iframe ${index} 发送消息`,
          )
          // 发送认证状态同步
          iframe.contentWindow.postMessage(
            {
              type: 'STATE_SYNC',
              payload: {
                auth: authData,
                user: userData,
                story: {
                  currentStory: localStorage.getItem('currentStory')
                    ? JSON.parse(localStorage.getItem('currentStory')!)
                    : null,
                  currentActor: localStorage.getItem('currentActor')
                    ? JSON.parse(localStorage.getItem('currentActor')!)
                    : null,
                },
              },
              timestamp: Date.now(),
              source: 'nuxt-app',
            },
            config.public.csrAppUrl,
          )
        } else {
          console.log(
            `[AUTH_SYNC] syncUserStateToApps: iframe ${index} 无效或未加载`,
          )
        }
      })
    }

    console.log('✅ 用户状态已同步到其他应用')
  } catch (error) {
    console.error('❌ 用户状态同步失败:', error)
  }
}

// 响应式数据
const loading = ref(false)
const countdown = ref(0)

interface FormState {
  email: string
  verificationCode: string
}

interface FormErrors {
  email?: string
  verificationCode?: string
}

const form = reactive<FormState>({
  email: '',
  verificationCode: '',
})

const errors = reactive<FormErrors>({
  email: '',
  verificationCode: '',
})

// 组件挂载时初始化用户状态
onMounted(() => {
  if (import.meta.client) {
    userStore.initFromStorage()
  }
})

// 验证邮箱
const validateEmail = () => {
  if (!form.email) {
    errors.email = 'Email is required'
    return false
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(form.email)) {
    errors.email = 'Invalid email format'
    return false
  }

  errors.email = ''
  return true
}

// 验证表单
const validateForm = (): boolean => {
  let isValid = true
  errors.verificationCode = ''

  // 验证邮箱
  if (!validateEmail()) {
    isValid = false
  }

  if (!form.verificationCode) {
    errors.verificationCode = 'Verification code is required'
    isValid = false
  }

  return isValid
}

// 处理社交登录
const handleSocialLogin = async (type: 'google' | 'discord' | 'facebook') => {
  if (loading.value) return

  loading.value = true
  try {
    console.log(`Social login with ${type}`)

    const origin = window.location.origin
    const redirect_url = `${origin}/user/social-callback?login_type=${type}`

    // 存储当前页面URL用于登录成功后重定向
    const app_redirect_url = window.location.href
    if (import.meta.client) {
      sessionStorage.setItem('login_redirect', app_redirect_url)
    }

    const config = useRuntimeConfig()
    const response = await $fetch(
      `${config.public.apiBase}/api/v1/social-login.get-url`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: {
          login_type: type,
          redirect_url,
        },
      },
    )

    if (response.code !== '0' || !response.data?.url) {
      throw new Error('Failed to get social login URL')
    }

    const loginUrl = response.data.url
    if (!loginUrl.startsWith('http')) {
      throw new Error('Invalid login URL')
    }

    // 跳转到社交登录页面
    window.location.href = loginUrl
  } catch (error: any) {
    console.error('Social login error:', error)
    showError(error.message || 'Social login failed')
  } finally {
    loading.value = false
  }
}

// 发送验证码
const handleSendCode = async () => {
  if (!validateEmail()) {
    showError(errors?.email || 'Invalid email')
    return
  }

  try {
    loading.value = true

    // 使用 userStore 的发送验证码方法
    await userStore.sendVerificationCode(form.email, 'login')

    // 开始倒计时
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)

    showSuccess('Verification code sent')
  } catch (error: any) {
    console.error('Send code error:', error)
    showError(error.message || 'Failed to send verification code')
  } finally {
    loading.value = false
  }
}

// 处理登录
const handleLogin = async () => {
  if (!validateForm()) return

  try {
    loading.value = true
    errors.verificationCode = ''

    // 使用 userStore 的登录方法
    const isLogin = await userStore.loginWithCode({
      email: form.email,
      code: form.verificationCode,
      code_type: 'login',
      gender: 'male',
      user_id: userStore.userInfo?.uuid,
    })

    if (isLogin) {
      console.log('Login successful')

      // 同步用户状态到其他应用
      await syncUserStateToApps()

      showSuccess('Login successful')

      // 登录成功后跳转
      router.push('/')
    }
  } catch (error: any) {
    console.error('Login error:', error)
    errors.verificationCode = error.message || 'Login failed'
  } finally {
    loading.value = false
  }
}

// SEO
useHead({
  title: 'Sign In - ' + websiteTitle,
  meta: [{ name: 'description', content: 'Sign in to your account' }],
})
</script>

<style lang="less" scoped>
.login-container {
  height: calc(var(--vh, 1vh) * 100);
  background: var(--mobile-bg-primary, #1f0038);
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  position: relative;
  padding-bottom: 80px;
  transition: background 0.3s ease;
}

.back-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary, rgba(255, 255, 255, 0.6));
  transition: all 0.2s;
  background: var(--bg-tertiary, rgba(255, 255, 255, 0.1));
  border-radius: 50%;
  flex-shrink: 0;

  &:hover {
    color: var(--text-primary, white);
    background: var(--bg-hover, rgba(255, 255, 255, 0.2));
  }
}

.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 320px;
  margin: 0 auto;
  width: 100%;
}

.logo-wrapper {
  margin-bottom: 48px;
  text-align: center;

  img {
    height: 35px;
    margin-bottom: 24px;
  }

  h2 {
    color: var(--text-primary, white);
    font-size: 18px;
    font-weight: 700;
    margin: 0;
    line-height: 1.5;
    background: linear-gradient(
      90deg,
      var(--accent-color, #ca93f2) 0%,
      var(--coins-color, #ffd700) 100%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 8px rgba(202, 147, 242, 0.3);
    animation: shine 2s infinite;

    @keyframes shine {
      0% {
        opacity: 1;
      }
      50% {
        opacity: 0.8;
      }
      100% {
        opacity: 1;
      }
    }
  }
}

.form-wrapper {
  width: 100%;

  .social-buttons {
    margin-bottom: 24px;
    animation: slideUp 0.5s ease;

    .social-row {
      display: flex;
      justify-content: space-between;
    }

    .social-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .social-name {
        color: var(--text-secondary, rgba(255, 255, 255, 0.6));
        font-size: 12px;
        font-weight: 500;
      }
    }

    .social-button {
      width: 64px;
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: white;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: none;
      padding: 0;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: 0.5s;
      }

      &:hover::before {
        left: 100%;
      }

      &:hover {
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(1px);
      }

      &.google {
        background: #fff;
        color: #333;
        box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
      }

      &.discord {
        background: #6563ff;
        box-shadow: 0 2px 8px rgba(88, 101, 242, 0.2);
      }

      &.facebook {
        background: #0866ff;
        box-shadow: 0 2px 8px rgba(24, 119, 242, 0.2);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: none;
      }
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .divider {
    display: flex;
    align-items: center;
    margin: 24px 0;

    &::before,
    &::after {
      content: '';
      flex: 1;
      height: 1px;
      background: var(--divider-color, rgba(255, 255, 255, 0.2));
    }

    span {
      padding: 0 16px;
      color: var(--text-tertiary, rgba(255, 255, 255, 0.4));
      font-size: 15px;
      white-space: nowrap;
      text-transform: lowercase;
    }
  }

  .input-group {
    margin-bottom: 16px;

    input {
      width: 100%;
      height: 48px;
      background: var(--mobile-input-bg, rgba(255, 255, 255, 0.1));
      border: 1px solid var(--mobile-input-border, rgba(255, 255, 255, 0.2));
      border-radius: 24px;
      padding: 0 20px;
      color: var(--text-primary, white);
      font-size: 15px;
      font-weight: 600;
      transition: all 0.3s;

      &::placeholder {
        color: var(--text-primary, white);
        font-size: 15px;
        font-weight: 600;
        line-height: normal;
        opacity: 0.3;
      }

      &:focus {
        outline: none;
        border-color: var(--accent-color, #ca93f2);
        background: var(--mobile-input-bg, rgba(255, 255, 255, 0.15));
      }

      &.error {
        border-color: rgba(255, 77, 77, 0.5);
      }
    }

    .error-text {
      color: rgba(255, 77, 77, 0.9);
      font-size: 12px;
      margin-top: 6px;
      margin-left: 4px;
      display: block;
    }
  }

  .verification-group {
    position: relative;

    .send-code-button {
      position: absolute;
      right: 8px;
      top: 24px;
      transform: translateY(-50%);
      background: var(--accent-color, #ca93f2);
      border: none;
      border-radius: 16px;
      padding: 6px 12px;
      color: var(--bg-primary, #1f0038);
      font-size: 14px;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s;
      z-index: 2;

      &.is-inactive {
        opacity: 0.5;
      }

      &:disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }
    }

    .error-text {
      margin-top: 4px;
      display: block;
      position: relative;
      z-index: 1;
    }
  }

  .continue-button {
    width: 100%;
    height: 48px;
    border: none;
    border-radius: 24px;
    background: var(--accent-color, #ca93f2);
    color: var(--bg-primary, #1f0038);
    font-size: 15px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &.is-inactive {
      opacity: 0.5;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    &:not(:disabled):hover {
      opacity: 0.9;
    }

    &:not(:disabled):active {
      transform: scale(0.98);
    }

    .animate-spin {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
  }
}

.terms-text {
  position: relative;
  margin-top: auto;
  padding: 24px 20px;
  color: var(--text-tertiary, rgba(255, 255, 255, 0.4));
  font-size: 12px;
  line-height: 1.5;
  text-align: center;

  a {
    color: var(--accent-color, #ca93f2);
    text-decoration: none;
    transition: opacity 0.3s;

    &:hover {
      text-decoration: underline;
    }
  }
}

/* 亮色主题适配 */
body.light-theme {
  .login-container {
    background: var(--mobile-bg-primary-light, #ffffff);

    .back-button {
      background: var(--bg-tertiary-light, rgba(0, 0, 0, 0.1));
      color: var(--text-secondary-light, rgba(0, 0, 0, 0.6));

      &:hover {
        background: var(--bg-hover-light, rgba(0, 0, 0, 0.2));
        color: var(--text-primary-light, #1a1a1a);
      }
    }

    .logo-wrapper h2 {
      color: var(--text-primary-light, #1a1a1a);
      text-shadow: 0 2px 8px rgba(202, 147, 242, 0.2);
    }

    .social-item .social-name {
      color: var(--text-secondary-light, rgba(0, 0, 0, 0.6));
    }

    .social-button {
      &.google {
        background: #fff;
        color: #333;
        border: 1px solid rgba(0, 0, 0, 0.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .divider {
      &::before,
      &::after {
        background: var(--divider-color-light, rgba(0, 0, 0, 0.1));
      }

      span {
        color: var(--text-tertiary-light, rgba(0, 0, 0, 0.4));
      }
    }

    .input-group input {
      background: var(--mobile-input-bg-light, rgba(0, 0, 0, 0.05));
      border-color: var(--mobile-input-border-light, rgba(0, 0, 0, 0.1));
      color: var(--text-primary-light, #1a1a1a);

      &::placeholder {
        color: var(--text-primary-light, #1a1a1a);
      }

      &:focus {
        border-color: var(--accent-color, #ca93f2);
        background: var(--mobile-input-bg-light, rgba(0, 0, 0, 0.08));
        box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.1);
      }
    }

    .continue-button {
      box-shadow: 0 2px 8px rgba(202, 147, 242, 0.2);

      &:hover:not(:disabled) {
        box-shadow: 0 4px 12px rgba(202, 147, 242, 0.3);
      }
    }

    .send-code-button {
      box-shadow: 0 2px 4px rgba(202, 147, 242, 0.2);
    }

    .terms-text {
      color: var(--text-tertiary-light, rgba(0, 0, 0, 0.5));

      a {
        color: var(--accent-color, #ca93f2);
      }
    }
  }
}
</style>
