<template>
  <div class="live-stream-video-background">
    <!-- 主视频 -->
    <video
      ref="primaryVideoRef"
      class="seamless-video primary"
      :class="{ active: currentVideo === 'primary' }"
      autoplay
      muted
      playsinline
      webkit-playsinline="true"
      x5-playsinline="true"
      x5-video-player-type="h5"
      x5-video-player-fullscreen="true"
      preload="auto"
      @ended="handleVideoEnded('primary')"
      @canplaythrough="handleCanPlayThrough('primary')"
      @error="handleError('primary')"
      @loadedmetadata="handleLoadedMetadata('primary')"
    />
    
    <!-- 副视频 -->
    <video
      ref="secondaryVideoRef"
      class="seamless-video secondary"
      :class="{ active: currentVideo === 'secondary' }"
      autoplay
      muted
      playsinline
      webkit-playsinline="true"
      x5-playsinline="true"
      x5-video-player-type="h5"
      x5-video-player-fullscreen="true"
      preload="auto"
      @ended="handleVideoEnded('secondary')"
      @canplaythrough="handleCanPlayThrough('secondary')"
      @error="handleError('secondary')"
      @loadedmetadata="handleLoadedMetadata('secondary')"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick, onBeforeUnmount } from 'vue'

interface Props {
  videoUrl?: string
}

const props = withDefaults(defineProps<Props>(), {
  videoUrl: ''
})

const emit = defineEmits<{
  'video-loaded': []
  'video-error': [error: string]
}>()

const primaryVideoRef = ref<HTMLVideoElement>()
const secondaryVideoRef = ref<HTMLVideoElement>()

// 当前活跃的视频
const currentVideo = ref<'primary' | 'secondary'>('primary')

// 视频准备状态
const videoReadyState = ref({
  primary: false,
  secondary: false
})

// 视频元数据加载状态
const videoMetadataLoaded = ref({
  primary: false,
  secondary: false
})

// 当前加载的视频URL，防止重复加载
let currentLoadingUrl = ''
let loadingPromise: Promise<void> | null = null

// 视频切换定时器
let switchTimer: number | null = null

/**
 * 获取当前活跃和待机的视频元素
 */
function getVideoElements() {
  const active = currentVideo.value === 'primary' ? primaryVideoRef.value : secondaryVideoRef.value
  const inactive = currentVideo.value === 'primary' ? secondaryVideoRef.value : primaryVideoRef.value
  const activeType = currentVideo.value
  const inactiveType = currentVideo.value === 'primary' ? 'secondary' : 'primary'
  
  return { active, inactive, activeType, inactiveType }
}

/**
 * 处理视频播放结束 - 作为备用切换机制
 */
function handleVideoEnded(videoType: 'primary' | 'secondary') {
  // 只有当前活跃视频结束时才作为备用切换
  if (videoType === currentVideo.value) {
    performSeamlessSwitch()
  }
}

/**
 * 处理视频准备就绪
 */
function handleCanPlayThrough(videoType: 'primary' | 'secondary') {
  videoReadyState.value[videoType] = true
}

/**
 * 处理视频元数据加载
 */
function handleLoadedMetadata(videoType: 'primary' | 'secondary') {
  videoMetadataLoaded.value[videoType] = true
  
  const videoElement = videoType === 'primary' ? primaryVideoRef.value : secondaryVideoRef.value
  if (videoElement && videoElement.duration) {
    // 如果是当前活跃视频，设置切换定时器
    if (videoType === currentVideo.value) {
      scheduleVideoSwitch(videoElement.duration)
    }
  }
  
  // 第一次加载时发出事件
  if (videoType === 'primary' && !videoMetadataLoaded.value.secondary) {
    emit('video-loaded')
  }
}

/**
 * 调度视频切换 - 在视频结束前100ms切换
 */
function scheduleVideoSwitch(duration: number) {
  if (switchTimer) {
    clearTimeout(switchTimer)
  }
  
  // 提前100ms切换，避免黑屏
  const switchTime = Math.max(0, (duration - 0.1) * 1000)
  
  switchTimer = window.setTimeout(() => {
    performSeamlessSwitch()
  }, switchTime)
}

/**
 * 执行无缝切换
 */
function performSeamlessSwitch() {
  const { active, inactive, activeType, inactiveType } = getVideoElements()
  
  if (inactive && videoReadyState.value[inactiveType] && inactive.src) {
    // 立即切换显示
    currentVideo.value = inactiveType
    
    // 确保新活跃视频正在播放
    if (inactive.paused) {
      inactive.play().catch(console.error)
    }
    
    // 将新活跃视频重置到开头
    inactive.currentTime = 0
    
    // 为新的活跃视频调度下次切换
    if (inactive.duration) {
      scheduleVideoSwitch(inactive.duration)
    }
  } else {
    // 如果没有准备好的下一个视频，重新播放当前视频
    if (active) {
      active.currentTime = 0
      if (active.paused) {
        active.play().catch(console.error)
      }
      
      // 重新调度当前视频的切换
      if (active.duration) {
        scheduleVideoSwitch(active.duration)
      }
    }
  }
}

/**
 * 处理视频错误
 */
function handleError(videoType: 'primary' | 'secondary') {
  videoReadyState.value[videoType] = false
  videoMetadataLoaded.value[videoType] = false
  emit('video-error', `Video error in ${videoType}`)
}

/**
 * 设置指定视频元素的视频源
 */
async function setVideoSource(videoType: 'primary' | 'secondary', url: string): Promise<void> {
  const videoElement = videoType === 'primary' ? primaryVideoRef.value : secondaryVideoRef.value
  
  if (!videoElement) {
    throw new Error(`${videoType}视频元素不存在`)
  }
  
  // 重置视频状态
  videoReadyState.value[videoType] = false
  videoMetadataLoaded.value[videoType] = false
  
  // 设置新的视频源
  videoElement.src = url
  videoElement.load()
  
  // 如果是非活跃视频，开始预播放
  if (videoType !== currentVideo.value) {
    try {
      await videoElement.play()
    } catch (playError) {
      // 预播放失败不影响整体流程
    }
  }
}

// 监听videoUrl变化 - 切换到下一个视频
watch(
  () => props.videoUrl,
  async (newUrl, oldUrl) => {
    if (!newUrl) return
    
    // 避免重复加载
    if (newUrl === oldUrl) {
      return
    }
    
    try {
      // 关键：新URL只设置给待机视频，实现真正的视频切换
      const { inactiveType } = getVideoElements()
      await setVideoSource(inactiveType, newUrl)
    } catch (error) {
      console.error('视频切换失败:', error)
    }
  },
  { immediate: false } // 不立即执行，等组件初始化完成
)

// 组件挂载时初始化
onMounted(async () => {
  await nextTick()
  
  // 如果有初始URL，只设置给主视频
  if (props.videoUrl) {
    try {
      await setVideoSource('primary', props.videoUrl)
    } catch (error) {
      console.error('主视频初始化失败:', error)
    }
  }
})

// 组件卸载时清理
onBeforeUnmount(() => {
  // 清理定时器
  if (switchTimer) {
    clearTimeout(switchTimer)
    switchTimer = null
  }
  
  // 停止并清理视频
  if (primaryVideoRef.value) {
    primaryVideoRef.value.pause()
    primaryVideoRef.value.src = ''
    primaryVideoRef.value.load()
  }
  
  if (secondaryVideoRef.value) {
    secondaryVideoRef.value.pause()
    secondaryVideoRef.value.src = ''
    secondaryVideoRef.value.load()
  }
  
  currentLoadingUrl = ''
  loadingPromise = null
})

// 暴露方法给父组件
defineExpose({
  setVideoSource,
  getCurrentVideo: () => currentVideo.value,
  getVideoReadyState: () => videoReadyState.value
})
</script>

<style scoped>
.live-stream-video-background {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  overflow: hidden;
  /* 强制禁用任何继承的过渡效果 */
  transition: none !important;
  transform: none !important;
  opacity: 1 !important;
}

.seamless-video {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  /* 强制移除任何过渡动画 */
  transition: none !important;
  animation: none !important;
  /* 硬件加速 */
  transform: translateZ(0) !important;
  will-change: auto !important;
  /* 强制显示，避免空白 */
  opacity: 1 !important;
  visibility: visible !important;
  /* 确保没有外边距和内边距 */
  margin: 0 !important;
  padding: 0 !important;
  /* 防止任何滤镜效果 */
  filter: none !important;
  backdrop-filter: none !important;
}

/* 主视频默认在上层 */
.seamless-video.primary {
  z-index: 2 !important;
}

.seamless-video.secondary {
  z-index: 1 !important;
}

/* 活跃视频显示 */
.seamless-video.active {
  z-index: 100 !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* 非活跃视频隐藏 */
.seamless-video:not(.active) {
  z-index: 1 !important;
  opacity: 0 !important;
  visibility: hidden !important;
  display: block !important;
}
</style>