<template>
  <div class="live-gift-animation">
    <!-- 礼物动画容器 -->
    <div
      v-for="animation in activeAnimations"
      :key="animation.id"
      class="gift-animation-item"
      :style="{ animationDelay: animation.delay + 'ms' }"
    >
      <!-- 礼物横幅 -->
      <div class="gift-banner">
        <div class="gift-info">
          <img :src="animation.gift.image_url" :alt="animation.gift.title" class="gift-icon" />
          <div class="gift-text">
            <div class="gift-title">{{ animation.gift.title }}</div>
            <div class="gift-sender">From {{ animation.senderName }}</div>
          </div>
        </div>
        <div class="gift-value">
          <img :src="icons.diamond.value" alt="Diamond" class="diamond-icon" />
          <span class="value-text">{{ animation.gift.coins }}</span>
        </div>
      </div>

      <!-- 礼物特效 -->
      <div class="gift-effects">
        <!-- 闪光特效 -->
        <div class="sparkle-container">
          <div class="sparkle sparkle-1">✨</div>
          <div class="sparkle sparkle-2">⭐</div>
          <div class="sparkle sparkle-3">✨</div>
          <div class="sparkle sparkle-4">💫</div>
          <div class="sparkle sparkle-5">✨</div>
        </div>

        <!-- 心形粒子 -->
        <div class="heart-burst">
          <div class="heart-particle heart-1">💖</div>
          <div class="heart-particle heart-2">💕</div>
          <div class="heart-particle heart-3">💖</div>
          <div class="heart-particle heart-4">💕</div>
          <div class="heart-particle heart-5">💖</div>
          <div class="heart-particle heart-6">💕</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import type { Present } from '@/api/chat-multivariate'
import { useIconConfig } from '@/composables/useIconConfig'

// 使用API中定义的Present类型
type Gift = Present

// 图标配置
const { icons } = useIconConfig()

// 动画数据类型
interface GiftAnimation {
  id: string
  gift: Gift
  senderName: string
  delay: number
}

// 活跃的动画列表
const activeAnimations = ref<GiftAnimation[]>([])

// 播放礼物动画
const playGiftAnimation = (gift: Gift, senderName: string = 'You') => {
  console.log('LiveGiftAnimation: playGiftAnimation called', { gift, senderName })

  const animationId = `gift_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`

  const animation: GiftAnimation = {
    id: animationId,
    gift,
    senderName,
    delay: 0
  }

  // 添加到活跃动画列表
  activeAnimations.value.push(animation)
  console.log(
    'LiveGiftAnimation: Animation added to list, total animations:',
    activeAnimations.value.length
  )

  // 3秒后移除动画
  setTimeout(() => {
    const index = activeAnimations.value.findIndex((a) => a.id === animationId)
    if (index > -1) {
      activeAnimations.value.splice(index, 1)
      console.log(
        'LiveGiftAnimation: Animation removed, remaining animations:',
        activeAnimations.value.length
      )
    }
  }, 3000) // 修复：改为3秒
}

// 暴露方法给父组件
defineExpose({
  playGiftAnimation
})
</script>

<style scoped lang="less">
.live-gift-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 20;
  overflow: hidden;
}

.gift-animation-item {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: giftShowcase 3s ease-out forwards;
}

.gift-banner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, rgba(76, 60, 89, 0.95) 0%, rgba(45, 27, 61, 0.95) 100%);
  border: 2px solid rgba(218, 255, 150, 0.8);
  border-radius: 16px;
  padding: 12px 16px;
  min-width: 280px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: bannerSlide 3s ease-out forwards;
}

.gift-info {
  display: flex;
  align-items: center;
  gap: 12px;

  .gift-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    object-fit: cover;
    border: 2px solid rgba(218, 255, 150, 0.5);
  }

  .gift-text {
    .gift-title {
      color: #daff96;
      font-family: 'Work Sans', sans-serif;
      font-weight: 700;
      font-size: 16px;
      margin-bottom: 2px;
    }

    .gift-sender {
      color: rgba(255, 255, 255, 0.8);
      font-family: 'Work Sans', sans-serif;
      font-weight: 400;
      font-size: 12px;
    }
  }
}

.gift-value {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(218, 255, 150, 0.2);
  padding: 6px 12px;
  border-radius: 12px;

  .diamond-icon {
    width: 20px;
    height: 20px;
  }

  .value-text {
    color: #daff96;
    font-family: 'Work Sans', sans-serif;
    font-weight: 700;
    font-size: 14px;
  }
}

.gift-effects {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
}

.sparkle-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.sparkle {
  position: absolute;
  font-size: 20px;
  animation: sparkleFloat 2s ease-out forwards;

  &.sparkle-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0.2s;
  }

  &.sparkle-2 {
    top: 30%;
    right: 15%;
    animation-delay: 0.4s;
  }

  &.sparkle-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 0.6s;
  }

  &.sparkle-4 {
    top: 60%;
    right: 10%;
    animation-delay: 0.8s;
  }

  &.sparkle-5 {
    bottom: 20%;
    right: 25%;
    animation-delay: 1s;
  }
}

.heart-burst {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
}

.heart-particle {
  position: absolute;
  font-size: 16px;
  animation: heartBurst 2.5s ease-out forwards;

  &.heart-1 {
    animation-delay: 0.5s;
    --x-offset: 80px;
    --y-offset: 0px;
  }

  &.heart-2 {
    animation-delay: 0.6s;
    --x-offset: 40px;
    --y-offset: -69px;
  }

  &.heart-3 {
    animation-delay: 0.7s;
    --x-offset: -40px;
    --y-offset: -69px;
  }

  &.heart-4 {
    animation-delay: 0.8s;
    --x-offset: -80px;
    --y-offset: 0px;
  }

  &.heart-5 {
    animation-delay: 0.9s;
    --x-offset: -40px;
    --y-offset: 69px;
  }

  &.heart-6 {
    animation-delay: 1s;
    --x-offset: 40px;
    --y-offset: 69px;
  }
}

@keyframes giftShowcase {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  15% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
  }
  25% {
    transform: translate(-50%, -50%) scale(1);
  }
  85% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

@keyframes bannerSlide {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  20% {
    transform: translateX(0);
    opacity: 1;
  }
  80% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes giftPulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes sparkleFloat {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
  100% {
    opacity: 0;
    transform: scale(0.5) rotate(360deg) translateY(-30px);
  }
}

@keyframes heartBurst {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0);
  }
  30% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5) translateX(var(--x-offset))
      translateY(var(--y-offset));
  }
}
</style>
