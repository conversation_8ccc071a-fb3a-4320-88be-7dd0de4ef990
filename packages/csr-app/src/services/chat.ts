import { useChatStore } from '@/store/chat'
import { useStoryStore } from '@/store'
import { useChatEventsStore } from '@/store/chat-events'
import { removeNullValues } from '@/utils/removeNullValues'
import { omitBy, isUndefined, isEmpty } from 'lodash-es'
import { EventSourcePolyfill } from '@/utils/EventSourcePolyfill'
import { SSEParams, StartChatParams } from '@/types/chat'
import { getDynamicApiHost } from '@/utils/dynamicApiHost'
import axios from 'axios'

/**
 * 开始新的对话（SSE）
 */
export async function startChatSSE(
  actorId: string,
  content: StartChatParams,
  onMessage: (data: any) => void,
  onError: (error: any) => void,
  onStreamClose?: () => void
) {
  const chatStore = useChatStore()
  const storyStore = useStoryStore()
  const chatEventStore = useChatEventsStore()
  const token = localStorage.getItem('token')

  // 根据游戏版本和重启状态确定API路径
  let apiUrl = ''

  // 根据演员版本或当前路由判断是否为chat4
  const isChat4 =
    storyStore.currentActor?.version === '4' ||
    storyStore.currentStory?.version === '4' ||
    window.location.pathname.includes('/chat4/')

  // 检查当前角色的版本是否为2或3，如果不是且不是Chat4，则强制设置为重启模式
  if (
    !isChat4 &&
    storyStore.currentActor?.version !== '2' &&
    storyStore.currentActor?.version !== '3'
  ) {
    chatEventStore.isShouldRestart = true
  }

  if (storyStore.currentStory?.version === '3') {
    // 版本3的游戏使用reasoning路径，不需要actorId
    apiUrl = chatEventStore.isShouldRestart
      ? `${getDynamicApiHost()}/api/v1/actor/${content.story_id}/reasoning/game.start`
      : `${getDynamicApiHost()}/api/v1/actor/event/reasoning/game.history`
  } else {
    // 其他版本使用原有路径
    apiUrl = chatEventStore.isShouldRestart
      ? `${getDynamicApiHost()}/api/v1/actor/${content.story_id}/${actorId}/game.start`
      : `${getDynamicApiHost()}/api/v1/actor/event/game.history`
  }

  const baseParams: Record<string, any> = {
    story_id: content.story_id
  }

  if (chatEventStore.isShouldRestart) {
    baseParams.restart = true
    baseParams.game_config = content.game_config
  } else if (storyStore.currentStory.version !== '3') {
    baseParams.actor_id = actorId
  }

  const params = omitBy(baseParams, isUndefined)

  // 创建自定义的 EventSource
  const es = new EventSourcePolyfill(apiUrl, {
    headers: {
      Authorization: `Bearer ${token}`
    },
    method: 'POST',
    body: JSON.stringify(removeNullValues(params)),
    isStartChat: true,
    onStreamClose
  })
  es.onmessage = (event) => {
    try {
      const data = JSON.parse(JSON.stringify(event.data))
      onMessage(data)
    } catch (err) {
      console.error('Failed to parse SSE message:', err)
    }
  }

  es.onerror = (error) => {
    onError(error)
    es.close()
  }

  // 返回清理函数
  return () => {
    es.close()
  }
}

/**
 * 送消息并接收流式响应
 */
export function sendMessageSSE(
  storyId: string,
  actorId: string,
  content: SSEParams,
  onMessage: (data: any) => void,
  onError: (error: any) => void,
  onStreamClose?: () => void
) {
  const token = localStorage.getItem('token')
  const storyStore = useStoryStore()

  // 根据游戏版本确定API路径
  let url = ''
  const apiHost = getDynamicApiHost()

  if (storyStore.currentStory?.version === '3') {
    // 版本3的游戏使用reasoning路径，不需要actorId
    url = `${apiHost}/api/v1/actor/${storyId}/reasoning/game.action`
  } else {
    // 其他版本使用原有路径
    url = `${apiHost}/api/v1/actor/${storyId}/${actorId}/game.action`
  }

  // 创建自定义的 EventSource
  const es = new EventSourcePolyfill(url, {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    method: 'POST',
    body: JSON.stringify(removeNullValues({ ...content })),
    onStreamClose
  })

  es.onmessage = (event) => {
    try {
      const data = JSON.parse(JSON.stringify(event.data))
      onMessage(data)
    } catch (err) {
      console.error('Failed to parse SSE message:', err)
    }
  }

  es.onerror = (error) => {
    console.error('Send message SSE Error:', error)
    onError(error)
    es.close()
  }

  // 返回清理函数
  return () => {
    es.close()
  }
}

/**
 * 通用SSE事件处理函数
 */
export function handleSSEEvents(
  events: any[],
  onComplete: (events: any[]) => void,
  onError: (error: any) => void
) {
  try {
    // 处理所有事件
    onComplete(events)
  } catch (error) {
    console.error('Error handling SSE events:', error)
    onError(error)
  }
}

/**
 * 通用场景跳转函数（SSE）
 */
export function jumpSceneSSE(
  storyId: string,
  action: 'prev_scene' | 'next_scene' | null,
  onMessage: (data: any) => void,
  onError: (error: any) => void,
  onStreamClose?: () => void,
  location?: string,
  actorId?: string
) {
  const token = localStorage.getItem('token')
  const storyStore = useStoryStore()

  // 根据游戏版本确定API路径
  const apiHost = getDynamicApiHost()
  let url = ''

  // 获取当前演员信息
  const currentActor = storyStore.currentActor
  const targetActorId = actorId || currentActor?.id

  // 根据演员版本或当前路由判断是否为chat4
  const isChat4 =
    currentActor?.version === '4' ||
    storyStore.currentStory?.version === '4' ||
    window.location.pathname.includes('/chat4/')

  if (isChat4 && targetActorId) {
    // Chat4版本使用 storyId/actorId 路径
    url = `${apiHost}/api/v1/actor/${storyId}/${targetActorId}/game.jump`
  } else {
    // 其他版本使用reasoning路径
    url = `${apiHost}/api/v1/actor/${storyId}/reasoning/game.jump`
  }

  // 构建请求体：如果没有action，则使用location参数
  const requestBody = action ? { action } : { location }

  // 创建自定义的 EventSource
  const es = new EventSourcePolyfill(url, {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    method: 'POST',
    body: JSON.stringify(removeNullValues(requestBody)),
    onStreamClose
  })

  const events: any[] = []

  es.onmessage = (event) => {
    try {
      const data = JSON.parse(JSON.stringify(event.data))
      events.push(data)
      onMessage(data)
    } catch (err) {
      console.error('Failed to parse SSE message:', err)
    }
  }

  es.onerror = (error) => {
    console.error(
      `Jump to ${action === 'prev_scene' ? 'previous' : 'next'} scene SSE Error:`,
      error
    )
    onError(error)
    es.close()
  }

  // 返回清理函数
  return () => {
    es.close()
  }
}

/**
 * 返回上一场景（SSE）
 */
export function jumpToPreviousSceneSSE(
  storyId: string,
  onMessage: (data: any) => void,
  onError: (error: any) => void,
  onStreamClose?: () => void,
  actorId?: string
) {
  return jumpSceneSSE(storyId, 'prev_scene', onMessage, onError, onStreamClose, undefined, actorId)
}

/**
 * 跳转到下一场景（SSE）
 */
export function jumpToNextSceneSSE(
  storyId: string,
  onMessage: (data: any) => void,
  onError: (error: any) => void,
  onStreamClose?: () => void,
  actorId?: string
) {
  return jumpSceneSSE(storyId, 'next_scene', onMessage, onError, onStreamClose, undefined, actorId)
}

/**
 * 跳转到指定场景（SSE）
 */
export function jumpToSceneSSE(
  storyId: string,
  location: string,
  onMessage: (data: any) => void,
  onError: (error: any) => void,
  onStreamClose?: () => void,
  actorId?: string
) {
  return jumpSceneSSE(storyId, null, onMessage, onError, onStreamClose, location, actorId)
}

// 视频加载服务
export const fetchVideo = async (url: string) => {
  const chatStore = useChatStore()
  chatStore.startVideoLoading()

  try {
    const response = await axios({
      url,
      method: 'GET',
      responseType: 'blob',
      onDownloadProgress: (progressEvent) => {
        if (progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          chatStore.updateVideoLoadingProgress(progress)
        }
      }
    })

    chatStore.finishVideoLoading()
    return response.data
  } catch (error) {
    chatStore.finishVideoLoading()
    throw error
  }
}
