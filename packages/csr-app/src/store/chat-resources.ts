import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { useStorage } from '@vueuse/core'
import { useAudioManager } from '@/mobile/composables/useAudioManager'
import { professionalVideoCache } from '@/utils/professionalVideoCache'
import { PreloadItem, UserAvatar } from './chat-types'
import type { ChatOptions, ActorOptions } from '@/types/chat'
import { useChatEventsStore } from './chat-events'

const audioManager = useAudioManager()

// 从URL加载资源到Blob - 优化版本，支持进度回调和取消
async function fetchResourceAsBlob(
  url: string,
  onProgress?: (loaded: number, total: number) => void,
  signal?: AbortSignal
): Promise<Blob | null> {
  try {
    const response = await fetch(url, { signal })
    if (!response.ok) {
      console.warn(`Failed to fetch resource: ${url}`)
      return null
    }

    const contentLength = response.headers.get('content-length')
    const total = contentLength ? parseInt(contentLength, 10) : 0

    if (!response.body || !onProgress || total === 0) {
      return await response.blob()
    }

    // 支持进度回调的流式读取
    const reader = response.body.getReader()
    const chunks: Uint8Array[] = []
    let loaded = 0

    try {
      let result = await reader.read()
      while (!result.done) {
        const { value } = result

        chunks.push(value)
        loaded += value.length

        // 调用进度回调
        onProgress(loaded, total)

        // 读取下一块数据
        result = await reader.read()
      }
    } finally {
      reader.releaseLock()
    }

    // 合并所有块
    const allChunks = new Uint8Array(loaded)
    let position = 0
    for (const chunk of chunks) {
      allChunks.set(chunk, position)
      position += chunk.length
    }

    return new Blob([allChunks])
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      console.log(`Resource fetch aborted: ${url}`)
      return null
    }
    console.error(`Error fetching resource: ${url}`, error)
    return null
  }
}

// 预加载并缓存视频资源 - 支持取消和进度
async function preloadAndCacheVideo(url: string, signal?: AbortSignal): Promise<void> {
  try {
    // 先检查专业缓存中是否已存在
    const cachedBlob = await professionalVideoCache.getCachedVideo(url)
    if (cachedBlob) {
      return
    }

    // 获取视频资源并缓存
    const blob = await fetchResourceAsBlob(url, undefined, signal)
    if (blob) {
      await professionalVideoCache.cacheVideo(url, blob)
    }
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      console.log(`Video preload aborted: ${url}`)
      return
    }
    console.warn(`Failed to preload and cache video: ${url}`, error)
  }
}

// 优化：预加载并缓存图片资源 - 移除重复的预加载步骤
async function preloadAndCacheImage(url: string, signal?: AbortSignal): Promise<void> {
  try {
    // 先检查专业缓存中是否已存在
    const cachedBlob = await professionalVideoCache.getCachedVideo(url)
    if (cachedBlob) {
      return
    }

    // 直接获取图片资源并缓存，不需要额外的预加载步骤
    const blob = await fetchResourceAsBlob(url, undefined, signal)
    if (blob) {
      await professionalVideoCache.cacheVideo(url, blob) // 复用专业缓存功能，因为底层存储机制相同
    }
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      console.log(`Image preload aborted: ${url}`)
      return
    }
    console.warn(`Failed to preload and cache image: ${url}`, error)
  }
}

/**
 * 获取视频的实际时长
 */
async function getVideoActualDuration(url: string): Promise<number> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    video.preload = 'metadata'

    const cleanup = () => {
      video.removeEventListener('loadedmetadata', onLoadedMetadata)
      video.removeEventListener('error', onError)
      video.src = ''
    }

    const onLoadedMetadata = () => {
      const duration = video.duration
      cleanup()
      if (isNaN(duration) || duration <= 0) {
        reject(new Error('Invalid video duration'))
      } else {
        console.log(`📹 视频时长获取成功: ${url} -> ${duration}秒`)
        resolve(duration)
      }
    }

    const onError = () => {
      cleanup()
      reject(new Error('Failed to load video metadata'))
    }

    video.addEventListener('loadedmetadata', onLoadedMetadata)
    video.addEventListener('error', onError)

    // 设置超时，避免无限等待
    setTimeout(() => {
      cleanup()
      reject(new Error('Timeout loading video metadata'))
    }, 10000) // 10秒超时

    video.src = url
  })
}

// 预加载资源
async function preloadResources(urls: PreloadItem[]): Promise<void> {
  const preloadPromises = urls.map((item) => {
    if (item.type === 'video') {
      return preloadAndCacheVideo(item.url)
    } else {
      return preloadAndCacheImage(item.url)
    }
  })

  await Promise.allSettled(preloadPromises)
}

// 智能预加载管理器
class SmartPreloadManager {
  private activeRequests = new Map<string, AbortController>()
  private loadingProgress = new Map<string, { loaded: number; total: number }>()

  async preloadWithProgress(url: string, type: 'video' | 'image'): Promise<void> {
    // 如果已经在加载，取消之前的请求
    if (this.activeRequests.has(url)) {
      this.activeRequests.get(url)?.abort()
    }

    const controller = new AbortController()
    this.activeRequests.set(url, controller)

    try {
      if (type === 'video') {
        await preloadAndCacheVideo(url, controller.signal)
      } else {
        await preloadAndCacheImage(url, controller.signal)
      }
    } finally {
      this.activeRequests.delete(url)
      this.loadingProgress.delete(url)
    }
  }

  cancelAll() {
    for (const controller of this.activeRequests.values()) {
      controller.abort()
    }
    this.activeRequests.clear()
    this.loadingProgress.clear()
  }

  getProgress(url: string) {
    return this.loadingProgress.get(url) || { loaded: 0, total: 0 }
  }
}

export const useChatResourcesStore = defineStore('chatResources', () => {
  // 创建智能预加载管理器实例
  const smartPreloadManager = new SmartPreloadManager()
  // 持久化存储
  const persistedAnimatedImagesMap = useStorage<Record<string, string[]>>(
    'chat-animated-images-map',
    {}
  )
  const persistedBackgroundImageMap = useStorage<Record<string, string>>(
    'chat-background-image-map',
    {}
  )
  const persistedBackgroundVideoMap = useStorage<Record<string, string>>(
    'chat-background-video-map',
    {}
  )
  const showEndingShare = useStorage<boolean>('chat-show-ending-share', false)

  // 状态
  const videoUrl = ref<string | null>(null)
  const isPlayingVideo = ref(false)
  const videoElementRef = ref<HTMLVideoElement | null>(null)
  const videoLoadingProgress = ref(0)
  const isVideoLoading = ref(false)
  const videoMinWatchDuration = ref(2)
  const isFirstVideo = ref(false)

  // 视频组播放状态
  const videoGroupQueue = ref<any[]>([])
  const currentVideoGroupIndex = ref(0)
  const isPlayingVideoGroup = ref(false)
  const shouldStopVideoGroup = ref(false)
  const pendingUserMessageForVideoGroup = ref<string | null>(null)
  const isVideoGroupPaused = ref(false) // 新增：视频组暂停状态
  const pausedVideoGroupState = ref<any>(null) // 新增：保存暂停时的状态

  const backgroundImageCouldBeFullScreen = ref(false)
  const isChatSectionVisible = ref(true)

  const preloadCache = ref<Set<string>>(new Set())
  const preloadingCount = ref(0)
  const isResourceLoading = ref(false)
  const isBackgroundTransitioning = ref(false) // 背景过渡状态

  // 模糊效果相关状态
  const backgroundBlurState = ref<{
    isBlurred: boolean
    tag?: string
    isBlurRequired: boolean
    currentHeartValue: number
    requiredHeartValue: number
  }>({
    isBlurred: false,
    tag: undefined,
    isBlurRequired: false,
    currentHeartValue: 0,
    requiredHeartValue: 0
  })

  const chatOptions = ref<ChatOptions[] | null>(null)
  const chatCard2 = ref<ChatOptions[] | null>(null)
  const chatActorOptions = ref<ActorOptions[] | null>(null)
  const actionOptions = ref<ChatOptions[] | null>(null)
  const heartValueOptionSceneIds = ref<string[]>([])
  const chatActorOptionsTooltip = ref<string | null>(null)

  const userAvatar = ref<UserAvatar | null>(null)

  const animatedImagesMap = ref<Record<string, string[]>>(persistedAnimatedImagesMap.value)
  const backgroundImageMap = ref<Record<string, string>>(persistedBackgroundImageMap.value)
  // 包含 url 和 notLoop 属性
  const backgroundVideoMap = ref<Record<string, { url: string; notLoop?: boolean }>>(
    Object.entries(persistedBackgroundVideoMap.value).reduce(
      (acc, [key, value]) => {
        acc[key] = { url: value }
        return acc
      },
      {} as Record<string, { url: string; notLoop?: boolean }>
    )
  )

  const currentActorId = ref<string | null>(null)
  const currentActorAvatarUrl = ref<string | null>(null)
  const currentActorName = ref<string | null>(null)
  const videoDescription = ref<string | null>(null)

  // Getters
  const animatedImages = computed(() =>
    currentActorId.value ? animatedImagesMap.value[currentActorId.value] || [] : []
  )

  const backgroundImage = computed(() =>
    currentActorId.value ? backgroundImageMap.value[currentActorId.value] || null : null
  )

  const backgroundVideo = computed(() => {
    const result = currentActorId.value
      ? backgroundVideoMap.value[currentActorId.value]?.url || ''
      : ''
    console.log('📺 [chat-resources] backgroundVideo computed:', {
      currentActorId: currentActorId.value,
      backgroundVideoMap: Object.keys(backgroundVideoMap.value).reduce((acc, key) => {
        acc[key] = backgroundVideoMap.value[key]?.url?.substring(0, 60) + '...'
        return acc
      }, {} as Record<string, string>),
      result: result?.substring(0, 60) + '...',
      timestamp: Date.now(),
      isVideoGroupPlaying: isPlayingVideoGroup.value,
      currentVideoIndex: currentVideoGroupIndex.value
    })
    return result
  })

  // Actions
  /**
   * 开始资源预加载
   */
  function startResourcePreloading() {
    preloadingCount.value++
    if (preloadingCount.value === 1) {
      isResourceLoading.value = true
    }
  }

  /**
   * 完成资源预加载
   */
  function finishResourcePreloading() {
    if (preloadingCount.value > 0) {
      preloadingCount.value--
      if (preloadingCount.value === 0) {
        isResourceLoading.value = false
      }
    }
  }

  /**
   * 预加载事件资源
   */
  async function preloadEventResources(event: any): Promise<void> {
    if (!event || !event.event_type) {
      return
    }

    const needsPreloading =
      (event.event_type === 'play_video' && event.data?.url) ||
      (event.event_type === 'play_video_group' && Array.isArray(event.data?.video_group)) ||
      (event.event_type === 'show_image' && event.data?.url) ||
      (event.event_type === 'animated_images' && Array.isArray(event.data?.urls)) ||
      (event.event_type === 'preload' && Array.isArray(event.data))

    if (!needsPreloading) {
      return
    }

    startResourcePreloading()

    try {
      if (event.event_type === 'play_video' && event.data.url) {
        await preloadAndCacheVideo(event.data.url)
      } else if (event.event_type === 'play_video_group' && Array.isArray(event.data.video_group)) {
        await preloadVideoGroup(event.data.video_group)
      } else if (event.event_type === 'show_image' && event.data.url) {
        await preloadAndCacheImage(event.data.url)
      } else if (event.event_type === 'animated_images' && Array.isArray(event.data.urls)) {
        const promises = event.data.urls.map((url: string) => preloadAndCacheImage(url))
        await Promise.all(promises)
      } else if (event.event_type === 'preload' && Array.isArray(event.data)) {
        await preloadResources(event.data)
      }
    } catch (error) {
      console.error(`Error preloading resources for event ${event.event_type}:`, error)
    } finally {
      finishResourcePreloading()
    }
  }

  /**
   * 预加载队列中的资源 - 优化版本，支持优先级和智能调度
   */
  async function preloadQueuedResources(messageQueue: any[]) {
    if (messageQueue.length === 0) return

    const resourceEvents = messageQueue.filter(
      (event) =>
        (event.event_type === 'play_video' && event.data?.url) ||
        (event.event_type === 'show_image' && event.data?.url) ||
        (event.event_type === 'animated_images' && Array.isArray(event.data?.urls)) ||
        (event.event_type === 'preload' && Array.isArray(event.data))
    )

    if (resourceEvents.length === 0) return

    startResourcePreloading()

    try {
      // 按优先级分组资源
      const highPriorityResources: Promise<void>[] = []
      const mediumPriorityResources: Promise<void>[] = []
      const lowPriorityResources: Promise<void>[] = []

      for (const event of resourceEvents) {
        if (event.event_type === 'play_video' && event.data.url) {
          // 视频资源优先级最高
          highPriorityResources.push(preloadAndCacheVideo(event.data.url))
        } else if (event.event_type === 'show_image' && event.data.url) {
          // 单张图片中等优先级
          mediumPriorityResources.push(preloadAndCacheImage(event.data.url))
        } else if (event.event_type === 'animated_images' && Array.isArray(event.data.urls)) {
          // 动画图片序列，第一张高优先级，其余低优先级
          const urls = event.data.urls
          if (urls.length > 0) {
            highPriorityResources.push(preloadAndCacheImage(urls[0]))
            for (let i = 1; i < urls.length; i++) {
              lowPriorityResources.push(preloadAndCacheImage(urls[i]))
            }
          }
        } else if (event.event_type === 'preload' && Array.isArray(event.data)) {
          for (const item of event.data) {
            if (item.url && item.type) {
              if (item.type === 'video') {
                mediumPriorityResources.push(preloadAndCacheVideo(item.url))
              } else {
                lowPriorityResources.push(preloadAndCacheImage(item.url))
              }
            }
          }
        }
      }

      // 分批处理，高优先级资源先加载
      const MAX_CONCURRENT_HIGH = 3
      const MAX_CONCURRENT_MEDIUM = 2
      const MAX_CONCURRENT_LOW = 1

      // 高优先级资源立即加载
      for (let i = 0; i < highPriorityResources.length; i += MAX_CONCURRENT_HIGH) {
        const batch = highPriorityResources.slice(i, i + MAX_CONCURRENT_HIGH)
        await Promise.allSettled(batch)
      }

      // 中等优先级资源并行加载
      const mediumPromise = (async () => {
        for (let i = 0; i < mediumPriorityResources.length; i += MAX_CONCURRENT_MEDIUM) {
          const batch = mediumPriorityResources.slice(i, i + MAX_CONCURRENT_MEDIUM)
          await Promise.allSettled(batch)
        }
      })()

      // 低优先级资源在后台慢慢加载
      const lowPromise = (async () => {
        for (let i = 0; i < lowPriorityResources.length; i += MAX_CONCURRENT_LOW) {
          const batch = lowPriorityResources.slice(i, i + MAX_CONCURRENT_LOW)
          await Promise.allSettled(batch)
          // 在低优先级资源之间添加小延迟，避免阻塞主线程
          await new Promise((resolve) => setTimeout(resolve, 100))
        }
      })()

      // 等待中等优先级完成，低优先级在后台继续
      await mediumPromise

      // 不等待低优先级完成，让它在后台继续
      lowPromise.catch((error) => {
        console.warn('Low priority resource preloading failed:', error)
      })
    } finally {
      finishResourcePreloading()
    }
  }

  /**
   * 处理显示图片事件
   */
  async function handleShowImageEvent(data: any) {
    // 停止 TTS 播放
    audioManager.stopTTS()
    if (currentActorId.value && backgroundVideoMap.value[currentActorId.value]) {
      backgroundVideoMap.value[currentActorId.value] = { url: '' }
    }
    // 设置新背景图片
    if (currentActorId.value) {
      backgroundImageMap.value[currentActorId.value] = data.data.url
    }
    backgroundImageCouldBeFullScreen.value = data.data.is_fullscreen

    // 处理模糊效果参数
    const isBlurRequired = !!data.data.is_blur
    const tag = data.data.tag

    console.log('handleShowImageEvent - Blur parameters:', {
      isBlurRequired,
      tag,
      url: data.data.url
    })

    // 获取当前心值和要求心值
    const currentHeartValue = await getCurrentHeartValue()
    const requiredHeartValue = tag ? await getRequiredHeartValue(tag) : 0
    if (data.data.description) {
      videoDescription.value = data.data.description
    }
    // 更新模糊状态
    backgroundBlurState.value = {
      isBlurred: isBlurRequired,
      tag: tag,
      isBlurRequired: isBlurRequired,
      currentHeartValue: currentHeartValue,
      requiredHeartValue: requiredHeartValue
    }

    // 如果有tag，检查心值要求来决定是否移除模糊
    if (tag && isBlurRequired) {
      checkHeartValueRequirement(tag)
        .then((shouldRemoveBlur) => {
          if (shouldRemoveBlur) {
            backgroundBlurState.value.isBlurred = false
          }
        })
        .catch((error) => {
          console.error('Error checking heart value requirement in handleShowImageEvent:', error)
        })
    }

    // 隐藏聊天区域
    isChatSectionVisible.value = false

    // 显示聊天区域
    isChatSectionVisible.value = true
  }

  /**
   * 处理动画图片事件
   */
  function handleAnimatedImagesEvent(data: any, isShouldRestart: boolean) {
    // 如果不需要重启，直接返回，因为已经在 processEvents 中处理为 show_image 了
    if (!isShouldRestart) {
      return Promise.resolve()
    }

    const { urls } = data.data
    if (currentActorId.value) {
      backgroundVideoMap.value[currentActorId.value] = { url: '' }
      animatedImagesMap.value[currentActorId.value] = urls
    }

    // 等待用户完成所有图片切换
    return new Promise<void>((resolve) => {
      // 如果没有图片，直接完成
      if (!urls || urls.length === 0) {
        resolve()
        return
      }

      // 监听器来检查 animatedImages 数组是否被清空
      const checkInterval = setInterval(() => {
        if (currentActorId.value && animatedImagesMap.value[currentActorId.value]?.length === 0) {
          clearInterval(checkInterval)
          resolve()
        }
      }, 100)
    })
  }

  /**
   * 处理播放视频事件
   */
  async function handlePlayVideoEvent(data: any) {
    // 如果视频无效，则不播放
    if (!data.data.url) {
      return
    }

    // 如果当前正在播放视频组且这是一个前景视频，暂停视频组播放而不是完全停止
    if (isPlayingVideoGroup.value && !data.data.is_background) {
      console.log('⏸️ 收到新的前景视频事件，暂停当前视频组播放以处理新事件')
      pauseVideoGroup()
      // 不要调用 stopVideoGroup()，这会完全停止循环
    }

    // 存储视频描述信息
    if (data.data.description) {
      videoDescription.value = data.data.description
    }

    // 背景视频
    if (data.data.is_background && currentActorId.value) {
      const oldVideoUrl = backgroundVideoMap.value[currentActorId.value]?.url || ''
      backgroundVideoMap.value[currentActorId.value] = {
        url: data.data.url,
        notLoop: !!data.data.not_loop
      }
      
      console.log('📺 [chat-resources] 背景视频URL更新:', {
        currentActorId: currentActorId.value,
        oldUrl: oldVideoUrl?.substring(0, 60) + '...',
        newUrl: data.data.url?.substring(0, 60) + '...',
        notLoop: !!data.data.not_loop,
        isVideoGroupPlaying: isPlayingVideoGroup.value,
        backgroundVideoAfterUpdate: backgroundVideo.value?.substring(0, 60) + '...',
        timestamp: Date.now()
      })

      // 处理背景视频的模糊效果参数
      const isBlurRequired = !!data.data.is_blur
      const tag = data.data.tag

      console.log('handlePlayVideoEvent - Background video blur parameters:', {
        isBlurRequired,
        tag,
        url: data.data.url
      })

      // 获取当前心值和要求心值
      const currentHeartValue = await getCurrentHeartValue()
      const requiredHeartValue = tag ? await getRequiredHeartValue(tag) : 0

      // 更新模糊状态
      backgroundBlurState.value = {
        isBlurred: isBlurRequired,
        tag: tag,
        isBlurRequired: isBlurRequired,
        currentHeartValue: currentHeartValue,
        requiredHeartValue: requiredHeartValue
      }

      // 如果有tag，检查心值要求来决定是否移除模糊
      if (tag && isBlurRequired) {
        checkHeartValueRequirement(tag)
          .then((shouldRemoveBlur) => {
            if (shouldRemoveBlur) {
              backgroundBlurState.value.isBlurred = false
            }
          })
          .catch((error) => {
            console.error('Error checking heart value requirement in handlePlayVideoEvent:', error)
          })
      }

      return
    }

    // 停止 TTS 播放
    audioManager.stopTTS()

    // 暂停 BGM
    audioManager.pauseBgm()

    // let _wasSkipped = false // 暂时不使用该变量

    try {
      // 设置最小可跳过时间
      videoMinWatchDuration.value = data.data?.min_watch_duration || 2

      videoUrl.value = data.data.url
      isPlayingVideo.value = true

      // 获取事件队列，用于预加载后续资源和处理背景事件
      const chatEventsStore = useChatEventsStore()

      // 在视频播放的同时预加载后续事件中的资源
      if (chatEventsStore.messageQueue.length > 0) {
        // 创建一个不阻塞的预加载任务
        const preloadTask = async () => {
          try {
            // 查找后续的资源事件
            const resourceEvents = chatEventsStore.messageQueue.filter(
              (event) =>
                (event.event_type === 'play_video' && event.data?.url) ||
                (event.event_type === 'show_image' && event.data?.url) ||
                (event.event_type === 'animated_images' && Array.isArray(event.data?.urls)) ||
                (event.event_type === 'preload' && Array.isArray(event.data))
            )

            if (resourceEvents.length > 0) {
              console.log(`开始预加载 ${resourceEvents.length} 个后续资源事件`)

              // 预加载资源，但不等待完成
              preloadQueuedResources(resourceEvents).catch((error) => {
                console.warn('预加载后续资源失败:', error)
              })
            }
            // 处理背景相关事件，使背景在视频播放的同时更新
            setTimeout(() => {
              chatEventsStore.processBackgroundEvents()
            }, 1000)
          } catch (error) {
            console.warn('预加载任务出错:', error)
          }
        }

        // 启动预加载任务，但不等待它完成
        preloadTask()
      }

      if (isFirstVideo.value) {
        await new Promise((resolve) => {
          const checkInterval = setInterval(() => {
            if (!isFirstVideo.value) {
              clearInterval(checkInterval)
              resolve(true)
            }
          }, 100)
        })
      }

      await Promise.race([
        new Promise((resolve) => {
          if (videoElementRef.value) {
            videoElementRef.value.onended = () => resolve(true)
          } else {
            const checkInterval = setInterval(() => {
              if (videoElementRef.value) {
                videoElementRef.value.onended = () => resolve(true)
                clearInterval(checkInterval)
              }
            }, 100)
          }
        }),
        new Promise((resolve) => {
          const unwatch = watch(
            () => isPlayingVideo.value,
            (newVal) => {
              if (!newVal) {
                // _wasSkipped = true // 暂时不使用该变量
                unwatch()
                resolve(true)
              }
            },
            { immediate: true }
          )
        })
      ])
    } catch (error) {
      console.error('Error loading video:', error)
    } finally {
      // 重置状态
      isPlayingVideo.value = false
      videoUrl.value = null
      isFirstVideo.value = false

      // 恢复 BGM 播放
      audioManager.playBgm()
    }
  }

  /**
   * 处理播放视频组事件
   */
  async function handlePlayVideoGroupEvent(data: any) {
    const videoGroup = data.data.video_group

    if (!videoGroup || !Array.isArray(videoGroup) || videoGroup.length === 0) {
      console.warn('Invalid video group data')
      return
    }

    // 如果当前正在播放视频组，先停止它
    if (isPlayingVideoGroup.value) {
      console.log('🛑 收到新的视频组事件，停止当前视频组播放')
      stopVideoGroup()
      // 等待一小段时间确保停止完成
      await new Promise((resolve) => setTimeout(resolve, 100))
    }

    console.log('🎬 开始播放视频组 - 详细分析:', {
      videoCount: videoGroup.length,
      currentActorId: currentActorId.value,
      backgroundVideosBefore: Object.keys(backgroundVideoMap.value),
      currentBackgroundVideo: backgroundVideo.value,
      videoDetails: videoGroup.map((v, index) => ({
        index,
        url: v.url?.substring(0, 60) + '...',
        is_background: v.is_background,
        hasIsBackgroundField: 'is_background' in v,
        isBackgroundType: typeof v.is_background
      }))
    })

    // 预加载所有视频
    await preloadVideoGroup(videoGroup)

    // 设置视频组队列
    videoGroupQueue.value = [...videoGroup]
    currentVideoGroupIndex.value = 0
    isPlayingVideoGroup.value = true
    shouldStopVideoGroup.value = false
    pendingUserMessageForVideoGroup.value = null

    // 开始播放视频组循环
    await playVideoGroupLoop()
  }

  /**
   * 预加载视频组中的所有视频
   */
  async function preloadVideoGroup(videoGroup: any[]) {
    console.log('🔄 开始预加载视频组...')

    // 预加载前景视频
    const foregroundPromises = videoGroup
      .filter((video) => video.url && !video.is_background)
      .map((video) => preloadAndCacheVideo(video.url))
      
    // 预加载背景视频
    const backgroundPromises = videoGroup
      .filter((video) => video.url && video.is_background)
      .map((video) => preloadAndCacheVideo(video.url))

    console.log('📊 预加载统计:', {
      foregroundVideos: foregroundPromises.length,
      backgroundVideos: backgroundPromises.length,
      totalVideos: videoGroup.length
    })

    const preloadPromises = [...foregroundPromises, ...backgroundPromises]

    try {
      await Promise.all(preloadPromises)
      console.log('✅ 视频组预加载完成')
    } catch (error) {
      console.warn('⚠️ 部分视频预加载失败:', error)
    }
  }

  /**
   * 播放视频组循环
   */
  async function playVideoGroupLoop() {
    while (isPlayingVideoGroup.value && !shouldStopVideoGroup.value) {
      console.log(`🔄 开始视频组循环，共 ${videoGroupQueue.value.length} 个视频`)

      for (let i = 0; i < videoGroupQueue.value.length; i++) {
        // 在每个视频播放前检查停止信号
        if (shouldStopVideoGroup.value || !isPlayingVideoGroup.value) {
          console.log('🛑 视频组播放被中断 (循环开始)')
          break
        }

        currentVideoGroupIndex.value = i
        const currentVideo = videoGroupQueue.value[i]

        console.log(
          `🎬 播放视频组第 ${i + 1}/${videoGroupQueue.value.length} 个视频:`,
          currentVideo.url
        )

        // 播放当前视频，并在即将结束时处理用户消息
        await playVideoWithUserMessageHandling(currentVideo)

        // 在每个视频播放后检查停止信号
        if (shouldStopVideoGroup.value || !isPlayingVideoGroup.value) {
          console.log('🛑 视频组播放被中断 (视频播放后)')
          break
        }
      }

      // 一轮播放完成，检查是否继续循环
      if (!shouldStopVideoGroup.value && isPlayingVideoGroup.value) {
        console.log('🔄 视频组一轮播放完成，开始下一轮循环')

        // 在开始下一轮循环前再次检查停止信号
        if (shouldStopVideoGroup.value || !isPlayingVideoGroup.value) {
          console.log('🛑 视频组播放被中断 (下一轮循环前)')
          break
        }
      }
    }

    // 清理状态
    console.log('🏁 视频组播放结束')
    isPlayingVideoGroup.value = false
    videoGroupQueue.value = []
    currentVideoGroupIndex.value = 0
    pendingUserMessageForVideoGroup.value = null
  }

  /**
   * 播放单个视频并处理用户消息
   */
  async function playVideoWithUserMessageHandling(videoData: any) {
    // 构造视频事件数据，复用现有的 handlePlayVideoEvent 逻辑
    const videoEventData = {
      data: videoData
    }

    // 如果是背景视频，设置背景并获取视频时长
    if (videoData.is_background) {
      console.log('📺 处理背景视频:', {
        url: videoData.url?.substring(0, 60) + '...',
        currentActorId: currentActorId.value,
        beforeUpdate: backgroundVideoMap.value[currentActorId.value || '']?.url?.substring(0, 60) + '...',
      })
      
      await handlePlayVideoEvent(videoEventData)
      
      console.log('📺 背景视频更新后:', {
        afterUpdate: backgroundVideoMap.value[currentActorId.value || '']?.url?.substring(0, 60) + '...',
        backgroundVideoComputed: backgroundVideo.value?.substring(0, 60) + '...'
      })

      // 获取视频的实际时长
      return new Promise<void>((resolve) => {
        const getVideoDuration = async () => {
          try {
            const videoDuration = await getVideoActualDuration(videoData.url)
            const minDuration = Math.max(videoDuration, videoData.min_watch_duration || 2)

            console.log(`⏳ 背景视频时长: ${videoDuration}秒, 最小播放时长: ${minDuration}秒`)

            let messageSent = false
            let timeoutId: NodeJS.Timeout

            // 在视频结束前2秒发送缓存的消息
            const sendMessageTime = Math.max(1, minDuration - 2) * 1000

            if (pendingUserMessageForVideoGroup.value && sendMessageTime > 0) {
              timeoutId = setTimeout(async () => {
                if (!messageSent && pendingUserMessageForVideoGroup.value) {
                  console.log(
                    '📤 背景视频即将结束，发送缓存的用户消息:',
                    pendingUserMessageForVideoGroup.value
                  )
                  messageSent = true
                  await sendCachedUserMessage()
                }
              }, sendMessageTime)
            }

            // 等待完整的播放时间
            const finalTimeout = setTimeout(() => {
              clearTimeout(timeoutId)
              resolve()
            }, minDuration * 1000)

            // 如果视频组被停止，立即清理并返回
            const stopWatcher = watch(
              () => shouldStopVideoGroup.value,
              (shouldStop) => {
                if (shouldStop) {
                  clearTimeout(timeoutId)
                  clearTimeout(finalTimeout)
                  stopWatcher()
                  resolve()
                }
              }
            )
          } catch (error) {
            console.warn('获取背景视频时长失败，使用默认时长:', error)
            const defaultDuration = videoData.min_watch_duration || 2
            setTimeout(resolve, defaultDuration * 1000)
          }
        }

        getVideoDuration()
      })
    }

    // 前景视频需要特殊处理
    return new Promise<void>((resolve) => {
      const playVideoAsync = async () => {
        // 设置视频播放
        videoMinWatchDuration.value = videoData?.min_watch_duration || 2
        videoUrl.value = videoData.url
        isPlayingVideo.value = true

        // 停止 TTS 播放
        audioManager.stopTTS()
        // 暂停 BGM
        audioManager.pauseBgm()

        let videoEndedNaturally = false
        let messageSentBeforeEnd = false

        // 监听视频时间更新，在即将结束时发送缓存的用户消息
        const handleTimeUpdate = () => {
          if (!videoElementRef.value || messageSentBeforeEnd) return

          const currentTime = videoElementRef.value.currentTime
          const duration = videoElementRef.value.duration
          const remainingTime = duration - currentTime

          // 在视频结束前2秒发送缓存的消息
          if (remainingTime <= 2 && pendingUserMessageForVideoGroup.value) {
            console.log(
              '⏰ 视频即将结束，发送缓存的用户消息:',
              pendingUserMessageForVideoGroup.value
            )
            sendCachedUserMessage()
            messageSentBeforeEnd = true
          }
        }

        // 等待视频元素准备就绪
        const waitForVideoElement = () => {
          return new Promise<void>((elementResolve) => {
            if (videoElementRef.value) {
              videoElementRef.value.addEventListener('timeupdate', handleTimeUpdate)
              elementResolve()
            } else {
              const checkInterval = setInterval(() => {
                if (videoElementRef.value) {
                  videoElementRef.value.addEventListener('timeupdate', handleTimeUpdate)
                  clearInterval(checkInterval)
                  elementResolve()
                }
              }, 100)
            }
          })
        }

        await waitForVideoElement()

        // 等待视频播放完成
        await Promise.race([
          new Promise((videoResolve) => {
            if (videoElementRef.value) {
              videoElementRef.value.onended = () => {
                videoEndedNaturally = true
                videoResolve(true)
              }
            }
          }),
          new Promise((skipResolve) => {
            const unwatch = watch(
              () => isPlayingVideo.value,
              (newVal) => {
                if (!newVal) {
                  unwatch()
                  skipResolve(true)
                }
              },
              { immediate: true }
            )
          })
        ])

        // 清理事件监听器
        if (videoElementRef.value) {
          videoElementRef.value.removeEventListener('timeupdate', handleTimeUpdate)
        }

        // 如果视频自然结束且有缓存消息但还没发送，立即发送
        if (videoEndedNaturally && pendingUserMessageForVideoGroup.value && !messageSentBeforeEnd) {
          console.log('🎬 视频播放完成，发送缓存的用户消息:', pendingUserMessageForVideoGroup.value)
          await sendCachedUserMessage()
        }

        // 重置视频状态
        isPlayingVideo.value = false
        videoUrl.value = null

        // 恢复 BGM 播放
        audioManager.playBgm()

        resolve()
      }

      // 执行异步逻辑
      playVideoAsync().catch((error) => {
        console.error('Error in playVideoWithUserMessageHandling:', error)
        // 确保即使出错也要重置状态
        isPlayingVideo.value = false
        videoUrl.value = null
        audioManager.playBgm()
        resolve()
      })
    })
  }

  /**
   * 发送缓存的用户消息并等待处理完成
   */
  async function sendCachedUserMessage() {
    if (!pendingUserMessageForVideoGroup.value) return

    const messageToSend = pendingUserMessageForVideoGroup.value
    pendingUserMessageForVideoGroup.value = null

    console.log('📤 发送缓存的用户消息:', messageToSend)

    // 发送消息给服务器
    const chatEventsStore = useChatEventsStore()
    await chatEventsStore.sendMessage(messageToSend)

    // 使用简化等待逻辑，避免死锁
    await waitForEventQueueCompleteSimple()
  }

  /**
   * 简化版等待函数 - 避免死锁问题
   */
  async function waitForEventQueueCompleteSimple() {
    console.log('⏳ 使用简化版等待逻辑，避免死锁...')

    // 检查是否有重要事件需要立即处理
    const chatEventsStore = useChatEventsStore()

    // 打印当前队列状态
    console.log('📋 当前事件队列状态:', {
      queueLength: chatEventsStore.messageQueue.length,
      events: chatEventsStore.messageQueue.map((event) => event.event_type)
    })

    // 检查视频相关事件
    const hasVideoEvents = chatEventsStore.messageQueue.some((event) =>
      ['play_video', 'play_video_group'].includes(event.event_type)
    )

    // 检查其他重要事件
    const hasImportantEvents = chatEventsStore.messageQueue.some((event) =>
      ['show_ending', 'show_overlay', 'show_chat_options', 'show_action_options'].includes(
        event.event_type
      )
    )

    if (hasVideoEvents) {
      console.log('🛑 检测到视频事件，暂停当前视频组让新事件处理')
      // 暂停当前视频组，让新的视频事件能够正常处理
      pauseVideoGroup()
      return
    }

    if (hasImportantEvents) {
      console.log('🛑 检测到重要事件，停止等待让事件正常处理')
      return
    }

    // 如果队列中有很多事件，也不要等待太久
    if (chatEventsStore.messageQueue.length > 3) {
      console.log('🛑 事件队列较长，缩短等待时间')
      await new Promise((resolve) => setTimeout(resolve, 1000)) // 只等待1秒
      console.log('✅ 缩短等待完成')
      return
    }

    // 等待固定时间，让事件处理完成
    console.log('⏳ 等待3秒让事件处理完成...')
    await new Promise((resolve) => setTimeout(resolve, 3000))

    console.log('✅ 简化版等待完成')
    
    // 检查是否需要恢复视频组播放
    if (isVideoGroupPaused.value) {
      console.log('🔄 事件处理完成，尝试恢复暂停的视频组')
      await resumeVideoGroup()
    }
  }

  /**
   * 停止视频组播放
   */
  function stopVideoGroup() {
    console.log('🛑 停止视频组播放')
    shouldStopVideoGroup.value = true
    isPlayingVideoGroup.value = false
    isVideoGroupPaused.value = false
    pausedVideoGroupState.value = null

    // 如果当前正在播放视频，也停止它
    if (isPlayingVideo.value) {
      isPlayingVideo.value = false
    }
  }

  /**
   * 暂停视频组播放
   */
  function pauseVideoGroup() {
    if (!isPlayingVideoGroup.value) return

    console.log('⏸️ 暂停视频组播放')

    // 保存当前状态
    pausedVideoGroupState.value = {
      videoGroupQueue: [...videoGroupQueue.value],
      currentVideoGroupIndex: currentVideoGroupIndex.value,
      pendingUserMessage: pendingUserMessageForVideoGroup.value
    }

    // 设置暂停状态
    isVideoGroupPaused.value = true
    shouldStopVideoGroup.value = true

    // 停止当前视频播放
    if (isPlayingVideo.value) {
      isPlayingVideo.value = false
    }

    console.log('⏸️ 视频组已暂停，状态已保存')
  }

  /**
   * 恢复视频组播放
   */
  async function resumeVideoGroup() {
    if (!isVideoGroupPaused.value || !pausedVideoGroupState.value) {
      console.log('⚠️ 没有可恢复的视频组状态')
      return
    }

    console.log('▶️ 恢复视频组播放')

    // 恢复状态
    videoGroupQueue.value = pausedVideoGroupState.value.videoGroupQueue
    currentVideoGroupIndex.value = pausedVideoGroupState.value.currentVideoGroupIndex
    pendingUserMessageForVideoGroup.value = pausedVideoGroupState.value.pendingUserMessage

    // 重置控制状态
    isVideoGroupPaused.value = false
    pausedVideoGroupState.value = null
    isPlayingVideoGroup.value = true
    shouldStopVideoGroup.value = false

    console.log('▶️ 恢复视频组播放，从第', currentVideoGroupIndex.value + 1, '个视频开始')

    // 继续播放视频组
    await playVideoGroupLoop()
  }

  /**
   * 处理视频组播放期间的用户输入
   */
  function handleUserInputDuringVideoGroup(message: string) {
    if (!isPlayingVideoGroup.value) {
      return false // 不在视频组播放期间，正常处理
    }

    // 缓存最后一条用户消息
    pendingUserMessageForVideoGroup.value = message
    console.log('💬 视频组播放期间收到用户消息，已缓存:', message)

    return true // 表示消息已被缓存
  }

  /**
   * 处理预加载事件
   */
  async function handlePreloadEvent(data: any) {
    if (Array.isArray(data.data)) {
      const resources = data.data.filter(
        (item: PreloadItem) => item.url && !preloadCache.value.has(item.url)
      )

      // Add URLs to cache first
      resources.forEach((item: PreloadItem) => {
        preloadCache.value.add(item.url)
      })

      // Then preload them
      await preloadResources(resources)
    }
  }

  /**
   * 处理显示聊天选项事件
   */
  function handleShowChatOptionsEvent(data: any) {
    const { style, options, tooltip } = data.data
    if (style === 'actor') {
      chatActorOptions.value = options
      chatActorOptionsTooltip.value = tooltip
    } else if (style !== 'card') {
      chatOptions.value = options
    } else {
      chatCard2.value = options
    }
  }

  /**
   * 处理显示动作选项事件
   */
  function handleShowActionOptionsEvent(data: any) {
    actionOptions.value = data.data.options
  }

  /**
   * 设置当前角色ID
   */
  function setCurrentActorId(actorId: string) {
    currentActorId.value = actorId
  }

  function setCurrentActorAvatarUrl(avatarUrl: string) {
    currentActorAvatarUrl.value = avatarUrl
  }

  function setCurrentActorName(name: string) {
    currentActorName.value = name
  }

  /**
   * 设置背景过渡状态
   */
  function setBackgroundTransitioning(transitioning: boolean) {
    isBackgroundTransitioning.value = transitioning
  }

  /**
   * 获取当前心值
   */
  async function getCurrentHeartValue(): Promise<number> {
    try {
      const chatEventsModule = await import('./chat-events')
      const chatEventsStore = chatEventsModule.useChatEventsStore()
      return chatEventsStore.favorabilityState.currentHeartValue || 0
    } catch (error) {
      console.error('Error getting current heart value:', error)
      return 0
    }
  }

  /**
   * 获取指定tag的要求心值
   */
  async function getRequiredHeartValue(tag: string): Promise<number> {
    try {
      const chatEventsModule = await import('./chat-events')
      const chatEventsStore = chatEventsModule.useChatEventsStore()
      const sceneActionsMenu = chatEventsStore.sceneActionsMenu || []

      const actionItem = sceneActionsMenu.find(
        (item: any) => item.key.toLowerCase() === tag.toLowerCase()
      )
      if (
        actionItem &&
        actionItem.requirement === 'heart_value' &&
        typeof actionItem.value === 'number'
      ) {
        return actionItem.value
      }
      return 0
    } catch (error) {
      console.error('Error getting required heart value:', error)
      return 0
    }
  }

  /**
   * 检查心值要求是否满足
   */
  async function checkHeartValueRequirement(tag: string): Promise<boolean> {
    try {
      // 从 chat-events store 获取心值和场景行为菜单数据
      // 使用动态导入避免循环依赖问题
      const chatEventsModule = await import('./chat-events')
      const chatEventsStore = chatEventsModule.useChatEventsStore()

      const currentHeartValue = chatEventsStore.favorabilityState.currentHeartValue || 0
      const sceneActionsMenu = chatEventsStore.sceneActionsMenu || []

      // 查找对应tag的心值要求
      console.log('Scene actions menu in checkHeartValueRequirement:', sceneActionsMenu)
      const actionItem = sceneActionsMenu.find(
        (item: any) => item.key.toLowerCase() === tag.toLowerCase()
      )

      if (!actionItem) {
        console.log(`No heart value requirement found for tag: ${tag}`)
        return true
      }

      // 检查是否满足心值要求
      if (actionItem.requirement === 'heart_value' && typeof actionItem.value === 'number') {
        const isUnlocked = currentHeartValue >= actionItem.value
        console.log(
          `Heart value check for tag ${tag}: current=${currentHeartValue}, required=${actionItem.value}, unlocked=${isUnlocked}`
        )
        return isUnlocked
      }

      return false
    } catch (error) {
      console.error('Error checking heart value requirement:', error)
      return false
    }
  }

  /**
   * 更新模糊状态基于当前心值
   * 当心值变化时调用此方法重新评估模糊状态
   */
  async function updateBlurStateBasedOnHeartValue() {
    if (backgroundBlurState.value.tag && backgroundBlurState.value.isBlurRequired) {
      try {
        // 更新当前心值
        const currentHeartValue = await getCurrentHeartValue()
        backgroundBlurState.value.currentHeartValue = currentHeartValue

        const shouldRemoveBlur = await checkHeartValueRequirement(backgroundBlurState.value.tag)
        if (shouldRemoveBlur && backgroundBlurState.value.isBlurred) {
          backgroundBlurState.value.isBlurred = false
          console.log(
            `Blur removed for tag ${backgroundBlurState.value.tag} due to heart value requirement met`
          )
        }
      } catch (error) {
        console.error(
          'Error checking heart value requirement in updateBlurStateBasedOnHeartValue:',
          error
        )
      }
    }
  }

  /**
   * 清空资源状态
   */
  // 监听 backgroundVideoMap 的变化，同步到 persistedBackgroundVideoMap
  watch(
    backgroundVideoMap,
    (newVal) => {
      // 将对象格式转换为字符串格式
      const stringMap: Record<string, string> = {}
      Object.entries(newVal).forEach(([key, value]) => {
        if (value && value.url) {
          stringMap[key] = value.url
        }
      })
      persistedBackgroundVideoMap.value = stringMap
    },
    { deep: true }
  )

  function clearResources() {
    // 取消所有正在进行的预加载请求
    smartPreloadManager.cancelAll()

    videoUrl.value = null
    isPlayingVideo.value = false
    chatOptions.value = null
    chatCard2.value = null
    chatActorOptions.value = null
    actionOptions.value = null
    currentActorId.value = null
    currentActorAvatarUrl.value = null
    currentActorName.value = null
    persistedBackgroundImageMap.value = {}
    persistedBackgroundVideoMap.value = {}
    persistedAnimatedImagesMap.value = {}
    backgroundVideoMap.value = {}

    // 清理视频组状态
    stopVideoGroup()
    videoGroupQueue.value = []
    currentVideoGroupIndex.value = 0
    pendingUserMessageForVideoGroup.value = null
  }

  return {
    // 状态
    videoUrl,
    isPlayingVideo,
    videoElementRef,
    videoLoadingProgress,
    isVideoLoading,
    videoMinWatchDuration,
    isFirstVideo,

    // 视频组状态
    videoGroupQueue,
    currentVideoGroupIndex,
    isPlayingVideoGroup,
    shouldStopVideoGroup,
    pendingUserMessageForVideoGroup,
    isVideoGroupPaused,
    pausedVideoGroupState,
    backgroundImageCouldBeFullScreen,
    isChatSectionVisible,
    preloadCache,
    preloadingCount,
    isResourceLoading,
    isBackgroundTransitioning, // 新增：背景过渡状态
    backgroundBlurState, // 新增：背景模糊状态
    chatOptions,
    chatCard2,
    chatActorOptions,
    chatActorOptionsTooltip,
    actionOptions,
    heartValueOptionSceneIds,
    userAvatar,
    animatedImagesMap,
    backgroundImageMap,
    backgroundVideoMap,
    currentActorId,
    currentActorAvatarUrl,
    currentActorName,
    videoDescription,
    showEndingShare,

    // Getters
    animatedImages,
    backgroundImage,
    backgroundVideo,

    // Actions
    startResourcePreloading,
    finishResourcePreloading,
    preloadEventResources,
    preloadQueuedResources,
    handleShowImageEvent,
    handleAnimatedImagesEvent,
    handlePlayVideoEvent,
    handlePlayVideoGroupEvent,
    handlePreloadEvent,
    handleShowChatOptionsEvent,
    handleShowActionOptionsEvent,

    // 视频组相关方法
    stopVideoGroup,
    pauseVideoGroup,
    resumeVideoGroup,
    handleUserInputDuringVideoGroup,
    setCurrentActorId,
    setCurrentActorAvatarUrl,
    setCurrentActorName,
    setBackgroundTransitioning, // 新增：设置背景过渡状态的方法
    checkHeartValueRequirement, // 新增：检查心值要求的方法
    updateBlurStateBasedOnHeartValue, // 新增：基于心值更新模糊状态的方法
    clearResources
  }
})
