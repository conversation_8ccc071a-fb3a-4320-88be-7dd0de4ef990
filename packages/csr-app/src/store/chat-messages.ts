import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Message } from '@/types/chat'
import { v4 as uuidv4 } from 'uuid'
import { useAudioManager } from '@/mobile/composables/useAudioManager'
import { VoiceConfig } from './chat-types'
import { useStoryStore } from '@/store/story'
import { useChatResourcesStore } from './chat-resources'
import { useChatUIStore } from './chat-ui'
import { useChat4Store } from './chat4'

// 创建音频管理器实例
const audioManager = useAudioManager()

export const useChatMessagesStore = defineStore('chatMessages', () => {
  // 状态
  const messages = ref<Message[]>([])
  const chatHistory = ref<Message[]>([])
  const isActorThinking = ref(false)
  const messageTypingPromise = ref<Promise<void> | null>(null)
  const messageTypingResolve = ref<((value: void | PromiseLike<void>) => void) | null>(null)
  const currentPlayingMessageId = ref<string | null>(null)
  const messageDelayTime = ref(300)
  const hasNextMessage = ref(false)
  const hasNextVideo = ref(false)
  const fullHeartValueAllowShowMessage = ref(false)
  const heartValue = ref(0)
  const voiceConfig = ref<VoiceConfig>({
    voice_id: null,
    provider: null
  })

  // Getters
  const sortedMessages = computed(() =>
    [...messages.value].sort(
      (a, b) => new Date(a.create_time).getTime() - new Date(b.create_time).getTime()
    )
  )

  // Actions
  /**
   * 处理消息事件
   */
  async function handleMessageEvent(data: any) {
    const chatResourcesStore = useChatResourcesStore()
    const storyStore = useStoryStore()
    const chatUIStore = useChatUIStore()

    const messageData = {
      ...data.data
    }

    // Handle hide_input parameter if present

    chatUIStore.setHideInput(data.data?.hide_input || false)

    // Guard clause: only process messages with text content
    if (!messageData.content.text) {
      return
    }

    // Process text by removing asterisks and trimming
    const processedText = messageData.content.text.replace(/\*(.*?)\*/g, '').trim()

    // Set thinking state while processing
    isActorThinking.value = true

    // Prepare TTS if there's processed text, message ID, and valid actor ID
    const actorId = chatResourcesStore.currentActorId || storyStore.currentActor?.id
    if (processedText && messageData.id && actorId) {
      // Request TTS but don't auto-play - ActorMessage component controls playback
      const ttsPromise = audioManager.prepareTTS(
        processedText,
        messageData.id,
        actorId,
        voiceConfig.value.voice_id,
        voiceConfig.value.provider
      )

      // Wait for TTS to load or timeout after 5 seconds
      const timeoutPromise = new Promise((resolve) => setTimeout(resolve, 5000))
      await Promise.race([ttsPromise, timeoutPromise]).catch((error) =>
        console.error('Failed to prepare TTS:', error)
      )
    }
    // End thinking state regardless of TTS success
    isActorThinking.value = false

    // 检查是否是Chat4模式（直播模式），如果是则跳过打字效果
    const isChat4Mode = storyStore.currentActor?.version === '4'

    if (isChat4Mode) {
      // Chat4直播模式：所有 actor 消息都通过 Chat4 Store 处理
      if (messageData.sender_type === 'actor') {
        console.log('Chat4 mode: sending actor message to Chat4 store:', messageData.id)
        // 使用新的 Chat4 store 处理消息
        const chat4Store = useChat4Store()
        chat4Store.addTTSMessage(messageData)
        // 重要：确保在返回前重置思考状态
        isActorThinking.value = false
        return
      }

      // 如果不是 actor 消息，直接添加消息
      console.log('chat4-messages: Adding non-actor message:', {
        msg_type: messageData.msg_type,
        sender_type: messageData.sender_type,
        content_text: messageData.content?.text?.substring(0, 50) || 'no text',
        totalMessagesAfter: messages.value.length + 1
      })
      messages.value.push(messageData)
      // 重要：确保在返回前重置思考状态
      isActorThinking.value = false
      // 直接返回，不等待打字效果
      return
    }

    // 非Chat4模式：创建打字动画Promise
    messageTypingPromise.value = new Promise((resolve) => {
      messageTypingResolve.value = resolve
    })

    // Add message to the message list
    console.log('chat4-messages: Adding actor message with typing effect:', {
      msg_type: messageData.msg_type,
      sender_type: messageData.sender_type,
      content_text: messageData.content?.text?.substring(0, 50) || 'no text',
      totalMessagesAfter: messages.value.length + 1
    })
    messages.value.push(messageData)

    // Guard clause: return if message is being skipped
    if (!messageTypingPromise.value) {
      return
    }

    // Wait for the configured delay before proceeding
    await new Promise((resolve) => setTimeout(resolve, messageDelayTime.value))
  }

  /**
   * 完成消息打字动画
   */
  async function completeMessageTyping(delay: number = 300, isSkipping: boolean = false) {
    // 无论是否有 resolve 函数，都要确保重置状态
    try {
      if (delay > 0 && !isSkipping) {
        // Add delay to give users time to read, but skip if we're in a skip scenario
        await new Promise((resolve) => setTimeout(resolve, delay))
      }

      // 调用 resolve 函数完成打字动画
      messageTypingResolve.value()
    } catch (error) {
      console.error('调用 messageTypingResolve 出错:', error)
    } finally {
      // 无论如何都要重置状态
      messageTypingResolve.value = null
      messageTypingPromise.value = null

      // Reset message delay time when skipping
      if (isSkipping) {
        messageDelayTime.value = 0 // Reset to default delay
      } else {
        // 根据下一个事件类型设置不同的延迟时间
        if (hasNextMessage.value) {
          messageDelayTime.value = 3000 // 如果下一个是消息，延迟更长
        } else if (hasNextVideo.value) {
          messageDelayTime.value = 3000 // 如果下一个是视频，延迟适中
        } else {
          messageDelayTime.value = 300 // 默认延迟
        }
      }
    }
  }

  /**
   * 处理心跳值事件
   */
  function handleHeartValueEvent(data: any) {
    heartValue.value = data.data.heart_value

    // 注意：这里暂时不处理 chatCard2 相关逻辑，因为它会导致循环依赖
    // 如果需要处理，应该在外部调用该函数的地方处理
    /*
    if (data.data.scene_ids && heartValue.value === 100) {
      // 在外部处理 chatCard2 相关逻辑
    }
    */

    fullHeartValueAllowShowMessage.value = data.data.is_allow_message
  }

  /**
   * 添加系统提示消息
   */
  function addTipsMessage(data: any) {
    const tipsMessage: Message = {
      id: uuidv4(),
      msg_type: 'text',
      sender_type: 'tips',
      content: {
        html: data.data.content.html
      },
      create_time: data.data.create_time,
      sender: {
        avatar_url: '',
        name: ''
      }
    }
    console.log('chat4-messages: Adding tips message:', {
      msg_type: tipsMessage.msg_type,
      sender_type: tipsMessage.sender_type,
      content_text: tipsMessage.content?.text?.substring(0, 50) || 'no text',
      totalMessagesAfter: messages.value.length + 1
    })
    messages.value.push(tipsMessage)
  }

  /**
   * 清空消息
   */
  function clearMessages() {
    console.log('chat4-messages: 🗑️ Clearing messages, current count:', messages.value.length)
    console.trace('chat4-messages: clearMessages called from:')
    messages.value = []
    console.log('chat4-messages: Messages cleared, new count:', messages.value.length)
  }

  /**
   * 设置语音配置
   */
  function setVoiceConfig(provider: string | null, voice_id: string | null) {
    voiceConfig.value = { provider, voice_id }
  }

  /**
   * 重置语音配置
   */
  function resetVoiceConfig() {
    voiceConfig.value = { provider: null, voice_id: null }
  }

  /**
   * 处理语音配置事件
   */
  function handleVoiceConfigEvent(data: any) {
    if (!data.data) return

    const { provider, voice_id } = data.data

    if (voice_id && provider) {
      console.log(`Setting voice configuration: provider=${provider}, voice_id=${voice_id}`)
      setVoiceConfig(provider, voice_id)
    } else {
      // 如果未提供voice_id，则重置为默认
      resetVoiceConfig()
    }
  }

  /**
   * 播放消息TTS
   */
  function playMessageTTS(message: any) {
    if (!message.content.audio_url) return

    // 直接使用audioManager，传递当前语音ID
    audioManager.playTTSFromUrl(
      message.content.audio_url,
      message.id,
      voiceConfig.value.voice_id,
      voiceConfig.value.provider
    )
  }

  return {
    // 状态
    messages,
    chatHistory,
    isActorThinking,
    messageTypingPromise,
    messageTypingResolve,
    currentPlayingMessageId,
    messageDelayTime,
    hasNextMessage,
    hasNextVideo,
    heartValue,
    fullHeartValueAllowShowMessage,
    voiceConfig,

    // Getters
    sortedMessages,

    // Actions
    handleMessageEvent,
    completeMessageTyping,
    handleHeartValueEvent,
    addTipsMessage,
    clearMessages,
    setVoiceConfig,
    resetVoiceConfig,
    handleVoiceConfigEvent,
    playMessageTTS
  }
})
