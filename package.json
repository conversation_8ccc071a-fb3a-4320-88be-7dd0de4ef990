{"name": "magic-partner-monorepo", "version": "1.0.0", "description": "Magic Partner Monorepo - CSR App & Nuxt App", "private": true, "scripts": {"dev": "turbo run dev --parallel", "dev:csr": "turbo run dev --filter=csr-app", "dev:nuxt": "turbo run dev --filter=nuxt-app", "dev:stripe": "VITE_PAYMENT_PROVIDER=stripe turbo run dev --filter=csr-app", "dev:onlypay": "VITE_PAYMENT_PROVIDER=onlypay turbo run dev --filter=csr-app", "dev:nuxt:stripe": "VITE_PAYMENT_PROVIDER=stripe NUXT_PUBLIC_PAYMENT_PROVIDER=stripe turbo run dev --filter=nuxt-app", "dev:nuxt:onlypay": "VITE_PAYMENT_PROVIDER=onlypay NUXT_PUBLIC_PAYMENT_PROVIDER=onlypay turbo run dev --filter=nuxt-app", "build": "turbo run build", "build:csr": "turbo run build --filter=csr-app", "build:nuxt": "turbo run build --filter=nuxt-app", "build:csr:prod": "turbo run build:prod.southeastAsia --filter=csr-app", "build:nuxt:staging": "turbo run build:staging --filter=nuxt-app", "build:nuxt:production": "turbo run build:production --filter=nuxt-app", "deploy:csr:turbo": "turbo run deploy --filter=csr-app", "deploy:nuxt:staging": "turbo run deploy:staging --filter=nuxt-app", "deploy:nuxt:production": "turbo run deploy:production --filter=nuxt-app", "deploy:nuxt": "turbo run deploy --filter=nuxt-app", "deploy:ssr:amplify": "echo 'Deploying SSR app...' && amplify publish", "deploy:csr:amplify": "echo 'Switching to CSR config...' && cp amplify-csr.yml amplify.yml && amplify publish && cp amplify-ssr.yml amplify.yml", "build:amplify": "pnpm run build:shared && cd packages/nuxt-app && pnpm run build:production", "build:amplify:csr": "pnpm run build:shared && cd packages/csr-app && pnpm run build:prod", "build:shared": "cd packages/shared-payment && pnpm run build", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "type:check": "turbo run type:check", "test": "turbo run test", "clean": "turbo run clean", "prepare": "husky install", "lint-staged": "lint-staged"}, "keywords": ["monorepo", "vue", "nuxt", "microfrontend"], "author": "Magic Partner Team", "license": "MIT", "devDependencies": {"@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@types/node": "^20.0.0", "concurrently": "^9.2.0", "husky": "^8.0.1", "lint-staged": "^13.0.3", "terser": "^5.30.3", "turbo": "^2.5.4", "typescript": "^5.0.0"}, "engines": {"node": ">=20.9.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.11.0", "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "gifsicle": "5.2.0"}, "lint-staged": {"packages/csr-app/**/*.{js,jsx,ts,tsx,vue}": ["pnpm --filter csr-app lint:fix"], "packages/nuxt-app/**/*.{js,jsx,ts,tsx,vue}": ["pnpm --filter nuxt-app lint:fix"]}, "dependencies": {"nuxt-gtag": "^3.0.3"}}